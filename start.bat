@echo off
echo Starting Solar Calculator with Outlook Integration...
echo.

REM Check if node_modules exists
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    echo.
)

REM Check if .env file exists
if not exist ".env" (
    echo WARNING: .env file not found!
    echo Please create a .env file with your Microsoft Graph credentials.
    echo See README.md for setup instructions.
    echo.
    pause
    exit /b 1
)

echo Starting server...
npm start
