{"name": "solar-calculator-outlook-integration", "version": "1.0.0", "description": "Solar calculator widget with Outlook calendar integration", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "node test-calendar.js", "test-calendar": "node test-calendar.js"}, "keywords": ["solar", "calculator", "outlook", "calendar", "microsoft-graph"], "author": "", "license": "ISC", "dependencies": {"@azure/identity": "^4.0.1", "@microsoft/microsoft-graph-client": "^3.0.7", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "node-fetch": "^2.7.0"}, "devDependencies": {"nodemon": "^3.0.2"}}