# Solar Calculator Widget with Outlook Calendar Integration

This widget allows users to find their location on a map, answer questions, choose appointment slots, fill out a form, and automatically creates events in your Outlook calendar.

## Features

- Interactive Google Maps location selection
- Multi-step questionnaire
- Real-time appointment slot availability
- Supabase database integration
- Email notifications via Brevo
- **NEW: Automatic Outlook calendar event creation**

## Setup Instructions

### Prerequisites

1. Node.js (version 14 or higher)
2. Microsoft 365 account with calendar access
3. Azure App Registration (for Microsoft Graph API)

### 1. Install Dependencies

```bash
npm install
```

### 2. Environment Variables

Your `.env` file should contain:

```
GOOGLE_MAPS_API=your_google_maps_api_key
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
BREVO_API_KEY=your_brevo_api_key

# Microsoft Graph API credentials
APPLICATION_ID=your_azure_app_client_id
SECRET_KEY=your_azure_app_client_secret
DIRECTORY_ID=your_azure_tenant_id
```

### 3. Azure App Registration Setup

To enable Outlook calendar integration, you need to set up an Azure App Registration:

1. Go to [Azure Portal](https://portal.azure.com)
2. Navigate to "Azure Active Directory" > "App registrations"
3. Click "New registration"
4. Fill in the details:
   - Name: "Solar Calculator Calendar Integration"
   - Supported account types: "Accounts in this organizational directory only"
   - Redirect URI: Leave blank for now
5. Click "Register"

After registration:

1. **Get Application (client) ID**: Copy this to your `.env` as `APPLICATION_ID`
2. **Get Directory (tenant) ID**: Copy this to your `.env` as `DIRECTORY_ID`
3. **Create Client Secret**:
   - Go to "Certificates & secrets"
   - Click "New client secret"
   - Add description and set expiration
   - Copy the secret value to your `.env` as `SECRET_KEY`

4. **Set API Permissions**:
   - Go to "API permissions"
   - Click "Add a permission"
   - Select "Microsoft Graph"
   - Select "Application permissions"
   - Add these permissions:
     - `Calendars.ReadWrite` (to create calendar events)
     - `User.Read.All` (to read user information)
   - Click "Grant admin consent"

### 4. Database Setup

Ensure your Supabase database has an `appointments` table with these columns:
- `id` (primary key)
- `appointment_datetime` (timestamp)
- `full_name` (text)
- `email` (text)
- `phone` (text)
- `postal_code` (text)
- `location_address` (text)
- `location_map_link` (text)
- `location_coords_lat` (numeric)
- `location_coords_lng` (numeric)
- `questionnaire_owner` (text)
- `questionnaire_installation` (text)
- `questionnaire_heating` (text)
- `questionnaire_household` (text)
- `created_at` (timestamp)

### 5. Run the Application

```bash
npm start
```

The application will be available at `http://localhost:3000`

For development with auto-restart:
```bash
npm run dev
```

## How It Works

1. **User Journey**:
   - User selects location on Google Maps
   - Answers questionnaire about property and preferences
   - Chooses available appointment slot
   - Fills out contact form
   - Submits booking

2. **Backend Process**:
   - Saves appointment to Supabase database
   - Sends email notification via Brevo
   - **Creates calendar event in your Outlook calendar**
   - Shows success message to user

3. **Calendar Event Details**:
   - Subject: "Solar Consultation - [Customer Name]"
   - Duration: 1 hour
   - Location: Customer's address
   - Attendees: Customer email
   - Body: All customer details and questionnaire responses
   - Reminder: 30 minutes before

## API Endpoints

- `GET /` - Serves the widget
- `POST /api/create-calendar-event` - Creates Outlook calendar event
- `GET /api/health` - Health check endpoint

## Troubleshooting

### Calendar Integration Issues

1. **"Microsoft Graph client not initialized"**:
   - Check your Azure credentials in `.env`
   - Ensure the Azure app has proper permissions
   - Verify admin consent was granted

2. **"Access denied" errors**:
   - Check API permissions in Azure
   - Ensure admin consent is granted
   - Verify the tenant ID is correct

3. **Calendar events not appearing**:
   - Check the calendar of the user associated with the Azure app
   - Verify the appointment datetime format
   - Check server logs for detailed error messages

### Testing

You can test the calendar integration by:
1. Making a booking through the widget
2. Checking the server console for logs
3. Verifying the event appears in your Outlook calendar
4. Using the health check endpoint: `GET /api/health`

## Security Notes

- The Microsoft Graph credentials are server-side only
- Client secrets should never be exposed to the frontend
- All calendar operations are performed server-side
- The widget communicates with the backend via secure API calls

## Support

If you encounter issues:
1. Check the server console logs
2. Verify all environment variables are set correctly
3. Test the health endpoint to ensure services are running
4. Check Azure app permissions and consent status
