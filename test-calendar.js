const fetch = require('node-fetch');

// Test data for calendar event creation
const testAppointmentData = {
    appointment_datetime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Tomorrow
    full_name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+447123456789",
    postal_code: "SW1A 1AA",
    location_address: "123 Test Street, London, UK",
    location_map_link: "https://maps.google.com/?q=123+Test+Street+London",
    location_coords_lat: 51.5074,
    location_coords_lng: -0.1278,
    questionnaire_owner: "yes",
    questionnaire_installation: "pitched-roof",
    questionnaire_heating: "heat-pump",
    questionnaire_household: "3"
};

async function testCalendarIntegration() {
    try {
        console.log('Testing calendar integration...');
        console.log('Test data:', JSON.stringify(testAppointmentData, null, 2));
        
        const response = await fetch('http://localhost:3000/api/create-calendar-event', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(testAppointmentData),
        });
        
        const result = await response.json();
        
        if (response.ok) {
            console.log('✅ Calendar event created successfully!');
            console.log('Event ID:', result.eventId);
            console.log('Web Link:', result.webLink);
        } else {
            console.log('❌ Failed to create calendar event');
            console.log('Error:', result.error);
        }
    } catch (error) {
        console.error('❌ Network error:', error.message);
        console.log('Make sure the server is running on http://localhost:3000');
    }
}

async function testHealthCheck() {
    try {
        console.log('\nTesting health check...');
        const response = await fetch('http://localhost:3000/api/health');
        const result = await response.json();
        
        if (response.ok) {
            console.log('✅ Health check passed');
            console.log('Status:', result.status);
            console.log('Graph Client Initialized:', result.graphClientInitialized);
        } else {
            console.log('❌ Health check failed');
        }
    } catch (error) {
        console.error('❌ Health check error:', error.message);
    }
}

// Run tests
async function runTests() {
    console.log('🧪 Testing Solar Calculator Outlook Integration\n');
    
    await testHealthCheck();
    await testCalendarIntegration();
    
    console.log('\n📝 Notes:');
    console.log('- Make sure your server is running: npm start');
    console.log('- Check your .env file has all required Microsoft Graph credentials');
    console.log('- Verify Azure app permissions are granted');
    console.log('- Check your Outlook calendar for the test event');
}

runTests();
