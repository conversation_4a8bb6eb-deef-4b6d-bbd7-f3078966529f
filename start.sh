#!/bin/bash

echo "Starting Solar Calculator with Outlook Integration..."
echo

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "Installing dependencies..."
    npm install
    echo
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "WARNING: .env file not found!"
    echo "Please create a .env file with your Microsoft Graph credentials."
    echo "See README.md for setup instructions."
    echo
    read -p "Press Enter to continue anyway or Ctrl+C to exit..."
fi

echo "Starting server..."
npm start
