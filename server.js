const express = require('express');
const cors = require('cors');
const { Client } = require('@microsoft/microsoft-graph-client');
const { ClientSecretCredential } = require('@azure/identity');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('.')); // Serve static files from current directory

// Microsoft Graph configuration
const clientId = process.env.APPLICATION_ID;
const clientSecret = process.env.SECRET_KEY;
const tenantId = process.env.DIRECTORY_ID;

// Initialize Microsoft Graph client
let graphClient;

try {
    const credential = new ClientSecretCredential(tenantId, clientId, clientSecret);
    graphClient = Client.initWithMiddleware({
        authProvider: {
            getAccessToken: async () => {
                const tokenResponse = await credential.getToken('https://graph.microsoft.com/.default');
                return tokenResponse.token;
            }
        }
    });
    console.log('Microsoft Graph client initialized successfully');
} catch (error) {
    console.error('Error initializing Microsoft Graph client:', error);
}

// Helper function to format date for Outlook
function formatDateForOutlook(isoString) {
    const date = new Date(isoString);
    return date.toISOString();
}

// Helper function to create calendar event
async function createCalendarEvent(appointmentData) {
    try {
        const startTime = new Date(appointmentData.appointment_datetime);
        const endTime = new Date(startTime.getTime() + (60 * 60 * 1000)); // 1 hour duration

        const event = {
            subject: `Solar Consultation - ${appointmentData.full_name}`,
            body: {
                contentType: 'HTML',
                content: `
                    <h3>Solar Consultation Appointment</h3>
                    <p><strong>Client:</strong> ${appointmentData.full_name}</p>
                    <p><strong>Email:</strong> ${appointmentData.email}</p>
                    <p><strong>Phone:</strong> ${appointmentData.phone}</p>
                    <p><strong>Postal Code:</strong> ${appointmentData.postal_code}</p>
                    <hr>
                    <p><strong>Location:</strong> ${appointmentData.location_address || 'Not specified'}</p>
                    ${appointmentData.location_map_link ? `<p><strong>Map Link:</strong> <a href="${appointmentData.location_map_link}">View on Map</a></p>` : ''}
                    <hr>
                    <h4>Questionnaire Responses:</h4>
                    <p><strong>Property Owner:</strong> ${appointmentData.questionnaire_owner || 'Not specified'}</p>
                    <p><strong>Installation Type:</strong> ${appointmentData.questionnaire_installation || 'Not specified'}</p>
                    <p><strong>Heating Type:</strong> ${appointmentData.questionnaire_heating || 'Not specified'}</p>
                    <p><strong>Household Size:</strong> ${appointmentData.questionnaire_household || 'Not specified'}</p>
                `
            },
            start: {
                dateTime: formatDateForOutlook(appointmentData.appointment_datetime),
                timeZone: 'Europe/London'
            },
            end: {
                dateTime: formatDateForOutlook(endTime.toISOString()),
                timeZone: 'Europe/London'
            },
            location: {
                displayName: appointmentData.location_address || 'Client Location'
            },
            attendees: [
                {
                    emailAddress: {
                        address: appointmentData.email,
                        name: appointmentData.full_name
                    },
                    type: 'required'
                }
            ],
            isReminderOn: true,
            reminderMinutesBeforeStart: 30,
            categories: ['Solar Consultation'],
            importance: 'normal'
        };

        console.log('Creating calendar event:', JSON.stringify(event, null, 2));
        
        const createdEvent = await graphClient.api('/me/events').post(event);
        console.log('Calendar event created successfully:', createdEvent.id);
        
        return {
            success: true,
            eventId: createdEvent.id,
            webLink: createdEvent.webLink
        };
    } catch (error) {
        console.error('Error creating calendar event:', error);
        throw error;
    }
}

// API endpoint to create calendar event
app.post('/api/create-calendar-event', async (req, res) => {
    try {
        console.log('Received calendar event request:', req.body);
        
        if (!graphClient) {
            throw new Error('Microsoft Graph client not initialized');
        }

        const appointmentData = req.body;
        
        // Validate required fields
        if (!appointmentData.appointment_datetime || !appointmentData.full_name || !appointmentData.email) {
            return res.status(400).json({
                success: false,
                error: 'Missing required fields: appointment_datetime, full_name, email'
            });
        }

        const result = await createCalendarEvent(appointmentData);
        
        res.json(result);
    } catch (error) {
        console.error('Error in create-calendar-event endpoint:', error);
        res.status(500).json({
            success: false,
            error: error.message || 'Failed to create calendar event'
        });
    }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        graphClientInitialized: !!graphClient
    });
});

// Serve the widget
app.get('/', (req, res) => {
    res.sendFile(__dirname + '/Widget.html');
});

app.listen(PORT, () => {
    console.log(`Server running on http://localhost:${PORT}`);
    console.log('Environment variables loaded:');
    console.log('- APPLICATION_ID:', clientId ? 'Set' : 'Not set');
    console.log('- SECRET_KEY:', clientSecret ? 'Set' : 'Not set');
    console.log('- DIRECTORY_ID:', tenantId ? 'Set' : 'Not set');
});
