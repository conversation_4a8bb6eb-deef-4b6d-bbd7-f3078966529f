<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Solar Calculator (Supabase Scheduler) - Google Maps</title>
    
    <!-- GOOGLE MAPS API - REPLACE YOUR_GOOGLE_MAPS_API_KEY -->
    <script async defer src="https://maps.googleapis.com/maps/api/js?key=AIzaSyC9c-7XPRyh9Ln6vadgIOFiK4lkLyBMQ5Y&libraries=marker,places,geocoding&callback=initMapAPIReady"></script>
    
    <!-- SUPABASE JS CLIENT -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>

    <style>
      .solar-calculator * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        font-family: system-ui, sans-serif;
    }

    .solar-calculator {
        width: 100%;
    }

    .solar-calculator-container {
        background-color: white;
        border-radius: 1rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        padding: 2rem;
        width: 100%;
        max-width: 1200px;
        overflow: hidden;
        position: relative;
        margin: 0 auto; /* Centers the card within its parent */
    }

    .solar-calculator-progress-bar {
        display: flex;
        justify-content: space-between;
        margin-bottom: 2rem;
        transition: opacity 0.3s ease, visibility 0.3s ease;
    }

    .solar-calculator-progress-step {
        display: flex;
        align-items: center;
        color: #999;
        font-size: 0.8rem; 
        flex: 1; 
        justify-content: center; 
        text-align: center; 
    }
     .solar-calculator-progress-step .solar-calculator-step-number {
        margin-bottom: 0.3rem; 
        margin-right: 0.3rem; 
    }

    .solar-calculator-progress-step.active {
        color: #000;
    }

    .solar-calculator-progress-step.completed {
        color: #666;
    }

    .solar-calculator-step-number {
        width: 1.5rem;
        height: 1.5rem;
        border: 2px solid currentColor;
        border-radius: 0.25rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
        flex-shrink: 0; 
    }
    @media (max-width: 768px) {
        .solar-calculator-progress-step {
            flex-direction: column;
            font-size: 0.7rem;
        }
        .solar-calculator-progress-step .solar-calculator-step-number {
            margin-right: 0;
            margin-bottom: 0.3rem;
        }
    }


    .solar-calculator-progress-step.completed .solar-calculator-step-number {
        background-color: #666;
        color: white;
    }

    .solar-calculator-progress-step.active .solar-calculator-step-number {
        background-color: #000;
        color: white;
    }

    .solar-calculator h2 {
        font-size: 2rem;
        margin-bottom: 1rem;
        font-weight: 500;
    }
    .solar-calculator h3 { /* For scheduler title */
        font-size: 1.75rem; /* Slightly smaller than h2 */
        margin-bottom: 1.5rem;
        font-weight: 500;
        text-align: center;
    }


    .solar-calculator p {
        color: #666;
        margin-bottom: 2rem;
    }

    .solar-calculator-options {
        display: flex;
        justify-content: space-between;
        gap: 1rem;
        margin-bottom: 2rem;
        flex-wrap: wrap; 
    }

    .solar-calculator-option {
        flex: 1 1 200px; 
        min-width: 150px; 
        border: 1px solid #ddd;
        border-radius: 0.5rem;
        padding: 1rem;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .solar-calculator-option:hover {
        background-color: #e67e22;
        color: white;
        transform: translateY(-5px);
        border-color: #e67e22;
    }

    .solar-calculator-option.selected {
        background-color: #e67e22;
        color: white;
        border-color: #e67e22;
    }

    .solar-calculator-option svg {
        width: 48px;
        height: 48px;
        margin-bottom: 0.5rem;
    }

    .solar-calculator-navigation {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 2rem;
    }

    .solar-calculator-back {
        color: #666;
        text-decoration: none;
        display: flex;
        align-items: center;
    }

    .solar-calculator-back svg {
        margin-right: 0.5rem;
    }

    .solar-calculator-balcony-link {
        color: #d35400;
        text-decoration: underline;
        cursor: pointer;
        font-size: 0.9rem;
        transition: color 0.3s ease;
    }

    .solar-calculator-balcony-link:hover {
        color: #e67e22;
    }

    .solar-calculator-results {
        background-color: #1a1a1a;
        color: white;
        border-radius: 1rem;
        padding: 2rem;
        opacity: 0;
        transform: translateX(100%);
        transition: transform 0.5s ease, opacity 0.5s ease;
        margin-bottom: 2rem; /* Added margin to separate from scheduler */
    }

    .solar-calculator-results.active {
        opacity: 1;
        transform: translateX(0);
    }

    .solar-calculator-savings-header {
        margin-bottom: 1rem;
    }

    .solar-calculator-savings-text {
        font-size: 1.2rem;
        margin-bottom: 0.5rem;
        line-height: 1.5;
    }

    .solar-calculator-savings-amount {
        font-size: 3rem;
        font-weight: bold;
        color: #e67e22;
        margin: 0.5rem 0;
    }

    .solar-calculator-savings-header span {
        color: #e67e22;
    }

    .solar-calculator-savings-breakdown {
        background-color: rgba(255, 255, 255, 0.1);
        height: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
        position: relative;
        overflow: hidden;
    }

    .solar-calculator-breakdown-segment {
        height: 100%;
        position: absolute;
        transition: width 1s ease; 
    }

    .solar-calculator-segment-1 {
        background-color: #f5d6a7;
        left: 0;
    }

    .solar-calculator-segment-2 {
        background-color: #e67e22;
    }

    .solar-calculator-segment-3 {
        background-color: #d35400;
    }

    .solar-calculator-breakdown-labels {
        display: flex;
        justify-content: space-between;
        font-size: 0.8rem;
        color: #999;
    }

    .solar-calculator-features {
        display: flex;
        justify-content: space-between;
        margin: 2rem 0;
        flex-wrap: wrap; 
        gap: 1rem; 
    }

    .solar-calculator-feature {
        display: flex;
        align-items: center;
        flex: 1 1 250px; 
    }


    .solar-calculator-feature svg {
        width: 48px;
        height: 48px;
        margin-right: 1rem;
        color: #e67e22;
    }

    .solar-calculator-feature-text {
        font-size: 0.9rem;
    }

    .solar-calculator-feature-value {
        color: #e67e22;
        font-weight: 500;
    }

    .solar-calculator-summary {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); 
        gap: 1rem;
        margin-top: 2rem;
    }

    .solar-calculator-summary-item {
        display: flex;
        align-items: center;
        font-size: 0.9rem;
        color: #999;
    }

    .solar-calculator-summary-item svg {
        width: 20px;
        height: 20px;
        margin-right: 0.5rem;
        color: #e67e22;
    }

    .solar-calculator-disclaimer {
        font-size: 0.8rem;
        color: #666;
        margin-top: 2rem;
    }

    .solar-calculator-steps-wrapper {
        position: relative;
        width: 100%;
        height: auto; 
        min-height: 300px; 
    }

    .solar-calculator-step,
    .solar-calculator-no {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.5s ease;
    }

    .solar-calculator-step.active,
    .solar-calculator-no.active {
        position: relative; 
        opacity: 1;
        pointer-events: auto;
    }
    .solar-calculator-container.no-active .solar-calculator-progress-bar {
        display: none;
    }
    .homepage-button{
        transition: background-color 0.3s ease, transform 0.3s ease;
      }
      .homepage-button:hover {
        transform: translateY(-6px); 
      }
      .homepage-button:active {
        transform: translateY(-5px); 
        background-color: #ccc; 
      }
    
    .solar-calculator-contact-form { 
        display: flex;
        flex-direction: column;
        gap: 1.5rem; /* Gap between form grid and send button */
    }
    .form-grid-2x2 {
        display: flex;
        flex-direction: column;
        gap: 1rem; /* Gap between the two rows of inputs */
    }

    .form-row {
        display: flex;
        gap: 1.5rem; /* Gap between items in a row (e.g., Name and Surname) */
    }

    .form-row .solar-calculator-form-group {
        flex: 1; /* Each group takes up equal space in the row */
        margin-bottom: 0; /* Remove bottom margin as gap is handled by form-grid-2x2 */
    }

    .solar-calculator-form-group label {
        font-size: 0.9rem;
        color: #333;
        margin-bottom: 0.5rem;
        font-weight: 500;
        display: block; /* Ensure label is on its own line */
    }
    .solar-calculator-form-group input {
        padding: 1rem;
        font-size: 1rem;
        border: 2px solid #e67e22;
        border-radius: 50px;
        outline: none;
        transition: all 0.3s ease;
        background-color: white;
        width: 100%; 
    }
    .solar-calculator-form-group input:focus {
        border-color: #d35400;
        box-shadow: 0 0 0 3px rgba(230, 126, 34, 0.1);
    }
    .input-with-icon {
        position: relative;
        display: flex;
        align-items: center;
    }
    .input-with-icon input {
        padding-right: 45px; /* Space for the icon + a bit of padding */
    }

    .solar-calculator-button {
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
        background-color: #e67e22;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        text-decoration: none;
        transition: background-color 0.3s ease, transform 0.2s ease;
        display: inline-block; 
        text-align: center;
    }
    .solar-calculator-button:hover {
        background-color: #d35400;
        transform: translateY(-3px);
    }
    .solar-calculator-button:active {
        transform: translateY(0);
    }
    .solar-calculator-button:disabled {
        background-color: #ccc;
        cursor: not-allowed;
        transform: translateY(0);
    }
   
    .hidden {
        display: none !important; 
    }
    .error-message {
        color: red;
        font-size: 0.9rem;
        margin-top: 0.5rem;
    }
    .thank-you-card {
        background-color: white;
        border-radius: 1rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        padding: 2rem;
        width: 100%;
        max-width: 600px;
        margin: 2rem auto;
        text-align: center;
        display: none; 
    }
    .thank-you-card.active {
        display: block;
    }
    .thank-you-card h2 {
        font-size: 2rem;
        margin-bottom: 1rem;
        font-weight: 500;
    }
    .thank-you-card p {
        color: #666;
        margin-bottom: 2rem;
    }

    #address-input-container {
        display: flex;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }
    #address-input {
        flex-grow: 1;
        padding: 0.8rem;
        font-size: 1rem;
        border: 1px solid #ccc;
        border-radius: 4px;
    }
    #search-address-btn { 
        padding: 0.8rem 1rem;
    }
    #map-canvas {
        height: 350px;
        width: 100%;
        border: 1px solid #ddd;
        border-radius: 4px;
        margin-bottom: 1rem;
        background-color: #e9e9e9;
    }
    .solar-calculator-confirm-location-button {
        margin-top: 1rem;
    }
    
    /* Style for Google Maps custom current location control */
    .custom-map-control-button {
      background-color: #fff;
      border: 0; 
      border-radius: 2px; 
      box-shadow: 0 1px 4px -1px rgba(0,0,0,.3); 
      cursor: pointer;
      margin: 10px; 
      padding: 0; /* Remove padding if emoji/icon is precise */
      text-align: center;
      font-size: 1.5em; /* Increased for better visibility of emoji */
      line-height: 34px; /* Vertically center emoji in button */
      height: 36px; 
      width: 36px;  
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .custom-map-control-button:hover {
      background-color: #f4f4f4;
    }


    .solar-calculator-scheduler-form {
        padding: 2rem;
        border-radius: 12px;
        background-color: white;
        margin-top: 2rem;
        box-shadow: 0 4px 12px rgba(0,0,0,0.05);
    }
    
    .scheduler-date-time-selection { 
        margin-bottom: 2rem; 
        position: relative; /* For calendar popup positioning */
    }

    .scheduler-date-navigation {
        display: flex;
        justify-content: center; /* Center the date display */
        align-items: center;
        margin-bottom: 1.5rem; 
        padding: 0.75rem 1rem; 
        background-color: #f8f8f8; 
        border-radius: 50px; 
    }
    .scheduler-nav-button { /* Now used for calendar month nav */
        background: #e67e22;
        color: white;
        border: none;
        padding: 0.6rem 0.9rem; 
        border-radius: 8px; 
        cursor: pointer;
        font-size: 1rem;
        font-weight: bold;
        transition: background-color 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .scheduler-nav-button:hover {
        background: #d35400;
    }
    .scheduler-nav-button:disabled {
        background: #e0e0e0; 
        color: #a0a0a0;
        cursor: not-allowed;
    }
    #selected-date-display {
        font-weight: 500; 
        font-size: 1.05rem; 
        color: #333;
        text-align: center;
        flex-grow: 1;
        cursor: pointer; /* Make it look clickable */
        padding: 0.5rem;
        border-radius: 4px;
        transition: background-color 0.2s ease;
    }
    #selected-date-display:hover {
        background-color: #efefef;
    }

    /* CALENDAR POPUP STYLES */
    .calendar-popup {
        position: absolute;
        top: calc(100% + 5px); /* Position below the date display */
        left: 50%;
        transform: translateX(-50%);
        background-color: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 1000;
        width: 320px; /* Adjust as needed */
        padding: 1rem;
    }
    .calendar-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }
    #calendar-month-year {
        font-weight: 500;
        font-size: 1.1rem;
    }
    .calendar-days-grid {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 5px;
        text-align: center;
    }
    .calendar-days-grid > div {
        padding: 0.5rem 0.25rem;
        font-size: 0.9rem;
        cursor: pointer;
        border-radius: 4px;
        transition: background-color 0.2s ease;
    }
    .calendar-days-grid > div.day-name {
        font-weight: bold;
        color: #666;
        cursor: default;
    }
    .calendar-days-grid > div:not(.day-name):not(.disabled):hover {
        background-color: #f0f0f0;
    }
    .calendar-days-grid > div.selected-cal-date {
        background-color: #e67e22;
        color: white;
    }
    .calendar-days-grid > div.today-cal-date {
        font-weight: bold;
        border: 1px solid #e67e22;
    }
    .calendar-days-grid > div.disabled {
        color: #ccc;
        cursor: not-allowed;
    }


    .time-slots-grid {
        display: flex; 
        flex-wrap: nowrap;
        justify-content: flex-start;
        gap: 0.75rem; 
        min-height: 40px; /* Ensure it has some height */
        overflow-x: auto; /* Enable scrolling */
        overflow-y: hidden;
        -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
        cursor: grab; /* Indicate it's draggable */
        user-select: none; /* Prevent text selection during drag */
    }
    .time-slots-grid::-webkit-scrollbar { display: none; }
    .time-slots-grid { -ms-overflow-style: none; scrollbar-width: none; }
    .time-slots-grid.dragging { cursor: grabbing; }

    .time-slot-button {
        padding: 0.75rem 1rem; 
        padding-left: 28px; 
        font-size: 0.9rem;
        border: 1px solid #e0e0e0; 
        border-radius: 50px; 
        background-color: #f7f7f7; 
        color: #333;
        cursor: pointer;
        text-align: left; 
        transition: all 0.2s ease;
        position: relative; 
        min-width: 130px; 
        flex-shrink: 0; 
    }
    .time-slot-button::before { 
        content: '';
        position: absolute;
        left: 12px; 
        top: 50%;
        transform: translateY(-50%);
        width: 3px; 
        height: 50%; 
        background-color: #cccccc; 
        border-radius: 2px;
    }

    .time-slot-button:hover:not(.disabled):not(.selected) { 
        background-color: #ededed; 
        border-color: #d0d0d0;
    }
    .time-slot-button.selected {
        background-color: #e67e22;
        color: white;
        font-weight: 500;
        border-color: #e67e22;
    }
    .time-slot-button.selected::before {
        background-color: rgba(255,255,255,0.7); 
    }
    .time-slot-button.disabled { 
        background-color: #f0f0f0;
        color: #999;
        cursor: not-allowed;
        opacity: 0.7;
    }
     .time-slot-button.disabled::before {
        background-color: #e0e0e0;
    }

    #scheduler-send-btn {
        width: 100%;
        max-width: 200px;
        margin: 1rem auto 0 auto; 
        padding: 1rem 2rem;
        border-radius: 50px;
        font-weight: 500;
        font-size: 1.1rem;
    }
    #time-slots-placeholder {
        color: #666;
        width: 100%;
        text-align: center;
        padding: 0.5rem 0;
    }
    /* Styles for Google Places Autocomplete dropdown */
    .pac-container {
        background-color: #FFF;
        z-index: 1050 !important; /* Ensure it's above other elements */
        border-radius: 8px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
        font-family: system-ui, sans-serif;
    }
    .pac-item {
        padding: 10px;
        font-size: 1rem;
        cursor: pointer;
        border-bottom: 1px solid #EEE;
    }
    .pac-item:last-child {
        border-bottom: none;
    }
    .pac-item:hover {
        background-color: #f0f0f0;
    }
    .pac-item-query {
        font-weight: bold;
    }
    .pac-icon {
        display: none; /* Hide default Places icon if not desired */
    }
    </style>
</head>
<body>
    <div class="solar-calculator">
        <div class="solar-calculator-container">
            <div class="solar-calculator-progress-bar">
                <div class="solar-calculator-progress-step" data-step="1"> <div class="solar-calculator-step-number">1</div> <span>Location</span> </div>
                <div class="solar-calculator-progress-step" data-step="2"> <div class="solar-calculator-step-number">2</div> <span>Owner</span> </div>
                <div class="solar-calculator-progress-step" data-step="3"> <div class="solar-calculator-step-number">3</div> <span>Installation</span> </div>
                <div class="solar-calculator-progress-step" data-step="4"> <div class="solar-calculator-step-number">4</div> <span>Heating</span> </div>
                <div class="solar-calculator-progress-step" data-step="5"> <div class="solar-calculator-step-number">5</div> <span>Household</span> </div>
                <div class="solar-calculator-progress-step" data-step="6"> <div class="solar-calculator-step-number">6</div> <span>Results & Schedule</span> </div>
            </div>

            <div class="solar-calculator-steps-wrapper">
                <!-- Step 1: Location -->
                <div class="solar-calculator-step" data-step="1">
                    <h2>Select Your Location (Google Maps Demo)</h2>
                    <p>Please search for your address or pinpoint your location on the map. This helps us provide a more accurate estimate.</p>
                    <div id="address-input-container">
                        <input type="text" id="address-input" placeholder="Enter address (e.g., London, UK)">
                        <button class="solar-calculator-button" id="search-address-btn">Search</button>
                    </div>
                    <div id="map-canvas"></div>
                    <button class="solar-calculator-button solar-calculator-confirm-location-button" id="confirm-location-btn">Confirm Location & Continue</button>
                </div>

                <!-- Step 2: Owner (Content unchanged) -->
                <div class="solar-calculator-step" data-step="2">
                    <h2>Do you own the house?</h2>
                    <p>To install a photovoltaic system, you must be the owner of the property.</p>
                    <div class="solar-calculator-options">
                        <div class="solar-calculator-option" data-value="yes"> <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"> <path d="M5 13l4 4L19 7"></path> </svg> <div>Yes</div> </div>
                        <div class="solar-calculator-option" data-value="no"> <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"> <path d="M18 6L6 18M6 6l12 12"></path> </svg> <div>No</div> </div>
                    </div>
                </div>

                <!-- Step 3: Installation (Content unchanged) -->
                <div class="solar-calculator-step" data-step="3">
                    <h2>Where would you like to install your solar system?</h2>
                    <p>The shape of the roof has an influence on the fastening method and the installation of your photovoltaic system.</p>
                    <div class="solar-calculator-options">
                        <div class="solar-calculator-option" data-value="gable"> <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"> <path d="M3 21h18M5 21V8l7-5 7 5v13"></path> </svg> <div>Pitched roof</div> </div>
                        <div class="solar-calculator-option" data-value="flat"> <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"> <path d="M3 21h18M5 21V6h14v15"></path> </svg> <div>Flat roof</div> </div>
                        <div class="solar-calculator-option" data-value="pult"> <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"> <path d="M3 21h18M5 21V11l14-5v15"></path> </svg> <div>Commercial roof</div> </div>
                        <div class="solar-calculator-option" data-value="other"> <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#000000"><path d="M160-120v-375l-72 55-48-64 440-336 440 336-48 63-72-54v375H160Zm80-80h480v-356L480-739 240-556v356Zm0 0h480-480Zm80-160q-17 0-28.5-11.5T280-400q0-17 11.5-28.5T320-440q17 0 28.5 11.5T360-400q0 17-11.5 28.5T320-360Zm160 0q-17 0-28.5-11.5T440-400q0-17 11.5-28.5T480-440q17 0 28.5 11.5T520-400q0 17-11.5 28.5T480-360Zm160 0q-17 0-28.5-11.5T600-400q0-17 11.5-28.5T640-440q17 0 28.5 11.5T680-400q0 17-11.5 28.5T640-360Z"/></svg> <div>Other</div> </div>
                    </div>
                </div>

                <!-- Step 4: Heating (Content unchanged) -->
                <div class="solar-calculator-step" data-step="4">
                    <h2>How is the house heated?</h2>
                    <p>The type of heating has an influence on the energy demand of your home.</p>
                    <div class="solar-calculator-options">
                        <div class="solar-calculator-option" data-value="heat-pump"> <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#000000"><path d="M280-216q-50 0-85-35t-35-85v-360h80v360q0 17 11.5 28.5T280-296q17 0 28.5-11.5T320-336v-240q0-50 35-85t85-35q50 0 85 35t35 85v240q0 17 11.5 28.5T600-296q17 0 28.5-11.5T640-336v-280q0-50 35-85t85-35h47l-47-47 56-57 144 144-144 143-56-57 47-46h-47q-17 0-28.5 11.5T720-616v280q0 50-35 85t-85 35q-50 0-85-35t-35-85v-240q0-17-11.5-28.5T440-616q-17 0-28.5 11.5T400-576v240q0 50-35 85t-85 35ZM120-96q-33 0-56.5-23.5T40-176v-320h880v320q0 33-23.5 56.5T840-96H120Zm0-80h720v-240H120v240Zm720-240H120h720Z"/></svg> <div>Heat pump</div> </div>
                        <div class="solar-calculator-option" data-value="oil-gas"> <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#000000"><path d="M160-120q-17 0-28.5-11.5T120-160q0-17 11.5-28.5T160-200h40v-240h-40q-17 0-28.5-11.5T120-480q0-17 11.5-28.5T160-520h40v-240h-40q-17 0-28.5-11.5T120-800q0-17 11.5-28.5T160-840h640q17 0 28.5 11.5T840-800q0 17-11.5 28.5T800-760h-40v240h40q17 0 28.5 11.5T840-480q0 17-11.5 28.5T800-440h-40v240h40q17 0 28.5 11.5T840-160q0 17-11.5 28.5T800-120H160Zm120-80h400v-240q-17 0-28.5-11.5T640-480q0-17 11.5-28.5T680-520v-240H280v240q17 0 28.5 11.5T320-480q0 17-11.5 28.5T280-440v240Zm200-120q50 0 85-34.5t35-83.5q0-39-22.5-67T480-620q-75 86-97.5 114.5T360-438q0 49 35 83.5t85 34.5ZM280-200v-560 560Z"/></svg> <div>Oil, gas</div> </div>
                        <div class="solar-calculator-option" data-value="district"> <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"> <path d="M3 12h18M7 8v8m5-8v8m5-8v8"></path> </svg> <div>Electric heating</div> </div>
                        <div class="solar-calculator-option" data-value="other"> <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#000000"><path d="M680-520v-120H560v-80h120v-120h80v120h120v80H760v120h-80ZM320-120q-83 0-141.5-58.5T120-320q0-48 21-89.5t59-70.5v-240q0-50 35-85t85-35q50 0 85 35t35 85v240q38 29 59 70.5t21 89.5q0 83-58.5 141.5T320-120Zm0-80q50 0 85-35t35-85q0-29-12.5-54T392-416l-32-24v-280q0-17-11.5-28.5T320-760q-17 0-28.5 11.5T280-720v280l-32 24q-23 17-35.5 42T200-320q0 50 35 85t85 35Zm0-120Z"/></svg> <div>Other</div> </div>
                    </div>
                </div>

                <!-- Step 5: Household (Content unchanged) -->
                <div class="solar-calculator-step" data-step="5">
                    <h2>How many people live in the household?</h2>
                    <p>Your information helps us to estimate the annual electricity consumption.</p>
                    <div class="solar-calculator-options">
                        <div class="solar-calculator-option" data-value="1-2"> <svg id="Icons_ManAndWoman" overflow="hidden" version="1.1" viewBox="0 0 96 96" xmlns="http://www.w3.org/2000/svg"><g><circle cx="32" cy="15" r="7"/><circle cx="64" cy="15" r="7"/><path d=" M 82.9 52.1 L 77.2 31 C 77 30.4 76.7 29.8 76.3 29.4 C 74.4 27.4 72 25.9 69.4 25 C 67.7 24.3 65.9 24 64 24 C 62.1 24 60.3 24.3 58.6 24.9 C 55.9 25.8 53.6 27.3 51.7 29.3 C 51.3 29.8 51 30.3 50.8 30.9 L 48 41.4 L 45.2 31 C 45 30.4 44.7 29.8 44.3 29.4 C 42.4 27.4 40 25.9 37.4 25 C 35.7 24.3 33.9 24 32 24 C 30.1 24 28.3 24.3 26.6 24.9 C 23.9 25.8 21.6 27.3 19.7 29.3 C 19.3 29.8 19 30.3 18.8 30.9 L 13.2 52 C 12.8 53.6 13.6 55.4 15.3 55.8 C 15.5 56 15.7 56 16 56 C 17.3 56 18.5 55.1 18.9 53.8 L 24 34.6 L 24 57 L 24 88 L 30 88 L 30 57 L 34 57 L 34 88 L 40 88 L 40 57 L 40 34.6 L 45.1 53.7 C 45.5 55 46.7 55.9 48 55.9 C 48.3 55.9 48.5 55.9 48.8 55.8 C 49.7 55.5 50.4 54.8 50.7 54 C 50.8 54 56 34.6 56 34.6 L 56 42.7 L 50.3 64 L 56 64 L 56 88 L 62 88 L 62 64 L 66 64 L 66 88 L 72 88 L 72 64 L 77.7 64 L 72 42.7 L 72 34.6 L 77.1 53.7 C 77.5 55 78.7 55.9 80 55.9 C 80.3 55.9 80.5 55.9 80.8 55.8 C 82.4 55.4 83.3 53.7 82.9 52.1 Z"/></g></svg> <div>1-2</div> </div>
                        <div class="solar-calculator-option" data-value="3"> <svg id="Icons_FamilyWithBoy" overflow="hidden" version="1.1" viewBox="0 0 96 96" xmlns="http://www.w3.org/2000/svg"><g><circle cx="23" cy="15" r="7"/><circle cx="53" cy="15" r="7"/><circle cx="80" cy="56" r="5"/><path d=" M 90.8 74.2 L 87.3 65.8 C 86.1 64.1 84.2 62.8 82.1 62.2 C 81.5 62.1 80.7 62 80 62 L 75 62 L 70.8 54.9 C 70.9 54.4 71 53.9 70.9 53.4 L 66.2 29.8 C 64.3 27.7 61.9 26.1 59.1 25.1 C 57.2 24.4 55.1 24 52.9 24 C 50.7 24 48.7 24.4 46.7 25.1 C 44 26.1 41.6 27.7 39.6 29.8 L 38 38.7 L 36.2 29.8 C 34.3 27.7 31.9 26.1 29.1 25.1 C 27.2 24.4 25.1 24 22.9 24 C 20.7 24 18.7 24.4 16.7 25.1 C 14 26.1 11.6 27.7 9.6 29.8 L 5.1 53.4 C 4.8 55 5.8 56.6 7.5 56.9 C 7.6 57 7.8 57 8 57 C 9.4 57 10.7 56 10.9 54.6 L 15 34.3 L 15 88 L 21 88 L 21 57 L 25 57 L 25 88 L 31 88 L 31 34.3 L 35.1 54.6 C 35.3 56 36.6 57 38 57 L 38 57 C 39.4 57 40.7 56 40.9 54.6 L 45 34.3 L 45 45.2 L 41.2 64.1 L 45 64.1 L 45 88 L 51 88 L 51 64 L 55 64 L 55 88 L 61 88 L 61 64.1 L 64.8 64.1 L 61 45.2 L 61 34.3 L 65.1 54.6 C 65.3 55.8 66.3 56.7 67.4 56.9 L 75 69.8 L 75 88 L 79 88 L 79 76 L 81 76 L 81 88 L 85 88 L 85 70.6 L 87.2 75.8 C 87.5 76.6 88.3 77 89 77 C 89.3 77 89.5 77 89.8 76.8 C 90.8 76.4 91.3 75.2 90.8 74.2 Z"/></g></svg> <div>3</div> </div>
                        <div class="solar-calculator-option" data-value="4"> <svg id="Icons_FamilyWithTwoChildren" overflow="hidden" version="1.1" viewBox="0 0 96 96" xmlns="http://www.w3.org/2000/svg"><g><circle cx="59" cy="23" r="5"/><circle cx="81" cy="47" r="4"/><circle cx="37" cy="23" r="5"/><circle cx="15" cy="47" r="4"/><path d=" M 91.8 64.2 L 88.3 55.9 C 87.1 54.2 85.2 52.9 83.1 52.3 C 82.5 52.1 81.7 52 81 52 C 79.6 52 78.3 52.3 77.1 52.9 L 71 48.6 L 67.4 33.6 C 67.3 33.2 67 32.7 66.7 32.5 C 65.5 31.6 64.1 30.9 62.6 30.5 C 61.4 30.2 60.2 30 59 30 C 57.8 30 56.6 30.2 55.5 30.5 C 54 30.9 52.6 31.6 51.4 32.5 C 51 32.8 50.8 33.2 50.7 33.6 L 48 44.4 L 45.4 33.6 C 45.3 33.2 45 32.7 44.7 32.5 C 43.5 31.6 42.1 30.9 40.6 30.5 C 39.4 30.2 38.2 30 37 30 C 35.8 30 34.6 30.2 33.5 30.5 C 32 30.9 30.6 31.6 29.4 32.5 C 29 32.8 28.8 33.2 28.7 33.6 L 25.1 48.6 L 19 52.9 C 17.7 52.3 16.4 52 15 52 C 14.3 52 13.5 52.1 12.9 52.3 C 10.8 52.8 8.9 54.1 7.7 55.9 L 4.2 64.2 C 3.8 65.1 4.1 66.1 4.8 66.7 C 5.2 66.9 5.6 67 6 67 C 6.8 67 7.5 66.5 7.8 65.8 L 10 60.6 L 10 66 L 10 78 L 14 78 L 14 66 L 16 66 L 16 78 L 20 78 L 20 57 L 20.1 57 L 28 51.5 C 28.4 51.2 28.7 50.8 28.8 50.3 L 32 37 L 32 78 L 36 78 L 36 55 L 38 55 L 38 78 L 42 78 L 42 37 L 46.1 53.5 C 46.3 54.4 47.1 55 48 55 C 48.9 55 49.7 54.4 49.9 53.5 L 54 37 L 54 45.4 L 50.5 60 L 54 60 L 54 78 L 58 78 L 58 60 L 60 60 L 60 78 L 64 78 L 64 60 L 67.5 60 L 64 45.4 L 64 37 L 67.3 50.3 C 67.4 50.8 67.7 51.2 68.1 51.5 L 76.1 57.1 L 76.1 63.3 L 74 69 L 76 69 L 76 78 L 80 78 L 80 69 L 82 69 L 82 78 L 86 78 L 86 69 L 88 69 L 86.1 63.7 L 86.1 60.6 L 88.3 65.8 C 88.6 66.6 89.4 67 90.1 67 C 90.5 67 90.9 66.9 91.3 66.6 C 91.9 66.1 92.2 65 91.8 64.2 Z"/></g></svg> <div>4</div> </div>
                        <div class="solar-calculator-option" data-value="5-plus"> <svg id="Layer_1" style="enable-background:new 0 0 128 128;" version="1.1" viewBox="0 0 128 128" xml:space="preserve" xmlns="http://www.w3.org/2000/svg"><g><g><path d="M36,117c-1.66,0-3-1.34-3-3v-6c0-2.76-2.24-5-5-5H12c-2.76,0-5,2.24-5,5v6c0,1.66-1.34,3-3,3s-3-1.34-3-3v-6    c0-6.07,4.93-11,11-11h16c6.07,0,11,4.93,11,11v6C39,115.66,37.66,117,36,117z"/></g><g><path d="M20,91c-7.17,0-13-5.83-13-13s5.83-13,13-13s13,5.83,13,13S27.17,91,20,91z M20,71c-3.86,0-7,3.14-7,7s3.14,7,7,7    s7-3.14,7-7S23.86,71,20,71z"/></g><g><path d="M80,117c-1.66,0-3-1.34-3-3v-6c0-2.76-2.24-5-5-5H56c-2.76,0-5,2.24-5,5v6c0,1.66-1.34,3-3,3s-3-1.34-3-3v-6    c0-6.07,4.93-11,11-11h16c6.07,0,11,4.93,11,11v6C83,115.66,81.66,117,80,117z"/></g><g><path d="M64,91c-7.17,0-13-5.83-13-13s5.83-13,13-13s13,5.83,13,13S71.17,91,64,91z M64,71c-3.86,0-7,3.14-7,7s3.14,7,7,7    s7-3.14,7-7S67.86,71,64,71z"/></g><g><path d="M124,117c-1.66,0-3-1.34-3-3v-6c0-2.76-2.24-5-5-5h-16c-2.76,0-5,2.24-5,5v6c0,1.66-1.34,3-3,3s-3-1.34-3-3v-6    c0-6.07,4.93-11,11-11h16c6.07,0,11,4.93,11,11v6C127,115.66,125.66,117,124,117z"/></g><g><path d="M108,91c-7.17,0-13-5.83-13-13s5.83-13,13-13s13,5.83,13,13S115.17,91,108,91z M108,71c-3.86,0-7,3.14-7,7s3.14,7,7,7    s7-3.14,7-7S111.86,71,108,71z"/></g><g><path d="M42,59c-7.17,0-13-5.83-13-13s5.83-13,13-13s13,5.83,13,13S49.17,59,42,59z M42,39c-3.86,0-7,3.14-7,7s3.14,7,7,7    s7-3.14,7-7S45.86,39,42,39z"/></g><g><path d="M86,59c-7.17,0-13-5.83-13-13s5.83-13,13-13s13,5.83,13,13S93.17,59,86,59z M86,39c-3.86,0-7,3.14-7,7s3.14,7,7,7    s7-3.14,7-7S89.86,39,86,39z"/></g><g><path d="M64,27c-7.17,0-13-5.83-13-13S56.83,1,64,1s13,5.83,13,13S71.17,27,64,27z M64,7c-3.86,0-7,3.14-7,7s3.14,7,7,7    s7-3.14,7-7S67.86,7,64,7z"/></g></g></svg> <div>5 or more</div> </div>
                    </div>
                </div>

                <!-- "If No" Path (Content unchanged) -->
                <div class="solar-calculator-no" data-step="if-no">
                    <h2>Together we will find a solution</h2>
                    <p>For the installation of a photovoltaic system, the homeowner must give his consent. If you are a tenant interested in solar power from your own roof, it is best to contact the person responsible for the property directly and draw his attention to our offer. Together we will find a solution to develop a suitable energy concept for you.</p>
                    <p>Further information for the homeowner can be found in our <a href="#" style="color:#d35400; text-decoration:underline">brochure</a></p>
                    <div> <button  class="homepage-button solar-calculator-button" style="width: auto; height:auto; background-color:#d35400; padding:0.8rem 1.5rem; "><a href="http://www.onesolarsystem.com/"  style="text-decoration: none; color:white;">To the Homepage</a></button> </div>
                </div>

                <!-- Step 6: Results & Scheduler (Content largely unchanged, email sending will be modified in script) -->
                <div class="solar-calculator-step" data-step="6">
                    <div class="solar-calculator-results"> 
                        <div class="solar-calculator-savings-header"> <div class="solar-calculator-savings-text"> <span>You save in</span><br> <span>20 years up to</span> </div> <div class="solar-calculator-savings-amount"> £0 </div> </div>
                        <div class="solar-calculator-savings-breakdown"> <div class="solar-calculator-breakdown-segment solar-calculator-segment-1"></div> <div class="solar-calculator-breakdown-segment solar-calculator-segment-2"></div> <div class="solar-calculator-breakdown-segment solar-calculator-segment-3"></div> </div>
                        <div class="solar-calculator-breakdown-labels"> <div>£0<br>Price signal capability</div> <div>+ £0<br>Feed-in tariff</div> <div>+ £0<br>Electricity cost savings</div> </div>
                        <div class="solar-calculator-features">
                            <div class="solar-calculator-feature"> <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#e8eaed"><path d="m422-232 207-248H469l29-227-185 267h139l-30 208ZM320-80l40-280H160l360-520h80l-40 320h240L400-80h-80Zm151-390Z"/></svg> <div class="solar-calculator-feature-text"> for up to<br> <span class="solar-calculator-feature-value">0% Autarky</span> </div> </div>
                            <div class="solar-calculator-feature"> <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#e8eaed"><path d="M440-360q-17 0-28.5-11.5T400-400v-160q0-17 11.5-28.5T440-600h120q17 0 28.5 11.5T600-560v160q0 17-11.5 28.5T560-360H440Zm20-60h80v-120h-80v120Zm-300 60q-17 0-28.5-11.5T120-400v-160q0-17 11.5-28.5T160-600h120q17 0 28.5 11.5T320-560v40h-60v-20h-80v120h80v-20h60v40q0 17-11.5 28.5T280-360H160Zm520 120v-100q0-17 11.5-28.5T720-380h80v-40H680v-60h140q17 0 28.5 11.5T860-440v60q0 17-11.5 28.5T820-340h-80v40h120v60H680Z"/></svg> <div class="solar-calculator-feature-text"> up to<br> <span class="solar-calculator-feature-value">0 kg CO² Savings</span> </div> </div>
                        </div>
                        <div class="solar-calculator-summary">
                            <div class="solar-calculator-summary-item"> <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"> <path d="M5 13l4 4L19 7"></path> </svg> <span>You are the owner</span> </div>
                            <div class="solar-calculator-summary-item"> <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"> <path d="M5 13l4 4L19 7"></path> </svg> <span>Your house is heated with...</span> </div>
                            <div class="solar-calculator-summary-item"> <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"> <path d="M5 13l4 4L19 7"></path> </svg> <span>Mounting on...</span> </div>
                            <div class="solar-calculator-summary-item"> <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"> <path d="M5 13l4 4L19 7"></path> </svg> <span>... people live in the household</span> </div>
                        </div>
                        <div class="solar-calculator-disclaimer"> All information is based on sample calculations and is without guarantee.* </div>
                    </div>

                    <div class="solar-calculator-scheduler-form" id="scheduler-form-container">
                        <h3>When are you available?</h3>
                        
                        <div class="scheduler-date-time-selection" id="dynamic-scheduler-parts">
                            <div class="scheduler-date-navigation">
                                <div id="selected-date-display" class="selected-date" role="button" tabindex="0">Select a Date</div>
                            </div>
                            
                            <div id="calendar-popup" class="calendar-popup hidden">
                                <div class="calendar-header">
                                    <button type="button" id="calendar-prev-month" class="scheduler-nav-button"><</button>
                                    <span id="calendar-month-year">Month Year</span>
                                    <button type="button" id="calendar-next-month" class="scheduler-nav-button">></button>
                                </div>
                                <div id="calendar-days" class="calendar-days-grid"></div>
                            </div>
                            
                            <div id="time-slots-container" class="time-slots-grid">
                                <p id="time-slots-placeholder" style="color: #666;">Please select a date to see available times.</p>
                            </div>
                        </div>

                        <form action="#" method="post" class="solar-calculator-contact-form" id="uk-contact-form">
                            <input type="hidden" id="selected-datetime-input" name="selected_datetime_combined">
                            
                            <div class="form-grid-2x2">
                                <div class="form-row">
                                    <div class="solar-calculator-form-group">
                                        <label for="fullName">Full Name</label>
                                        <input type="text" id="fullName" name="fullName" placeholder="Enter your full name" required>
                                    </div>
                                    <div class="solar-calculator-form-group">
                                        <label for="email">E-mail</label>
                                        <div class="input-with-icon">
                                            <input type="email" id="email" name="email" placeholder="Enter your email address" required>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-row">
                                    <div class="solar-calculator-form-group">
                                        <label for="phone">Phone</label>
                                        <input type="tel" id="phone" name="phone" placeholder="Enter your UK phone number" required>
                                    </div>
                                    <div class="solar-calculator-form-group">
                                        <label for="postalCode">Postal Code</label>
                                        <input type="text" id="postalCode" name="postalCode" placeholder="Enter your UK postal code" required>
                                    </div>
                                </div>
                            </div>
                                                        
                            <div style="text-align: center;">
                                <button type="submit" id="scheduler-send-btn" class="solar-calculator-button">Send</button>
                            </div>
                        </form>
                        
                    </div> 
                </div> 
            </div> 
        
            <div class="solar-calculator-navigation">
                <a href="#" class="solar-calculator-back"> <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" width="24" height="24"> <path d="M19 12H5M12 19l-7-7 7-7"></path> </svg> BACK </a>
                <a href="#" class="solar-calculator-balcony-link">Interested in a balcony plant?</a>
                <button id="outside-consultation-button" class="solar-calculator-button navigation-consultation-button hidden"> Request Free Consultation </button>
            </div>
        </div> 

        <div class="thank-you-card" id="thank-you-card">
            <h2>Thank you for your request!</h2>
            <p>We will now take care of your request as soon as possible. One of our specialists will contact you and provide you with any further information regarding your appointment.</p>
            <a href="https://onesolarsystem.com" class="solar-calculator-button homepage-button">To the Homepage</a>
        </div>
    </div> 
    
    <script>
    // --- SUPABASE CLIENT INITIALIZATION ---
    const SUPABASE_URL = 'https://srljywabzrlgvzwweayh.supabase.co';
    const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNybGp5d2FienJsZ3Z6d3dlYXloIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3Mjc2NzIsImV4cCI6MjA2NDMwMzY3Mn0.bkd1n15yOJ7WsWMtMkbnXwvMkV-UUx31zpFOGycaUOQ';
    const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

    // --- START GOOGLE MAPS LOGIC ---
    let googleMap;
    let googleMarker; // This will be an AdvancedMarkerElement
    let geocoder;     // Google Geocoder instance
    let autocomplete; // Google Places Autocomplete instance
    let googleMapsApiReady = false;
    let mapControlsInitialized = false; // To ensure custom controls are added only once

    function initMapAPIReady() { // Called when Google Maps API script has loaded
        googleMapsApiReady = true;
        geocoder = new google.maps.Geocoder(); // Initialize Geocoder

        // If step 1 is active when API loads, try to init map
        if (typeof scState !== 'undefined' && scState.currentStep === 1 &&
            document.querySelector('.solar-calculator-step[data-step="1"].active') &&
            !googleMap) { // Check if map isn't already initialized
            initGoogleMap();
        }
    }

    async function initGoogleMap() {
        if (!googleMapsApiReady) {
            console.warn("Google Maps API not ready. Deferring map initialization.");
            return;
        }
        if (!geocoder) {
            console.warn("Geocoder not ready, attempting to initialize again.");
            geocoder = new google.maps.Geocoder();
            if (!geocoder) {
                console.error("Failed to initialize Geocoder in initGoogleMap.");
                alert("Map services (Geocoder) could not be initialized. Address search may not work.");
            }
        }

        if (googleMap) { // If map already exists, just ensure it's sized and centered
            google.maps.event.trigger(googleMap, 'resize');
            if (scState.locationCoords && scState.locationCoords.lat && scState.locationCoords.lng) {
                const pos = new google.maps.LatLng(scState.locationCoords.lat, scState.locationCoords.lng);
                googleMap.setCenter(pos);
                if (googleMarker) googleMarker.position = pos;
            }
            return;
        }

        const mapCanvas = document.getElementById('map-canvas');
        const addressInput = document.getElementById('address-input');
        const searchAddressBtn = document.getElementById('search-address-btn');
        const confirmLocationBtn = document.getElementById('confirm-location-btn');

        if (!mapCanvas || !addressInput || !searchAddressBtn || !confirmLocationBtn || !scState) {
            console.error("Map dependencies not found or scState undefined.");
            return;
        }

        const defaultLat = 51.505, defaultLng = -0.09; // London
        let initialPosition = new google.maps.LatLng(defaultLat, defaultLng);
        let initialZoom = 10;

        if (scState.locationCoords && scState.locationCoords.lat && scState.locationCoords.lng) {
            initialPosition = new google.maps.LatLng(scState.locationCoords.lat, scState.locationCoords.lng);
            initialZoom = 15;
            if (addressInput && !addressInput.value && scState.locationAddress) { 
                addressInput.value = scState.locationAddress; 
            }
        }

        googleMap = new google.maps.Map(mapCanvas, {
            center: initialPosition,
            zoom: initialZoom,
            mapId: "SOLAR_CALC_MAP_ID", 
            mapTypeId: google.maps.MapTypeId.HYBRID,
            mapTypeControl: true,
            streetViewControl: false,
            fullscreenControl: true,
            zoomControl: true,
        });

        await createOrUpdateGoogleMarker(initialPosition);

        // Initialize Google Places Autocomplete
        autocomplete = new google.maps.places.Autocomplete(
            addressInput,
            {
                types: ['address'],
                // componentRestrictions: { country: "uk" }, // Optional: Restrict to a specific country
            }
        );
        autocomplete.setFields(['formatted_address', 'geometry', 'name']); // Crucial for billing and getting needed data

        autocomplete.addListener('place_changed', () => {
            const place = autocomplete.getPlace();
            if (!place.geometry || !place.geometry.location) {
                console.warn("Autocomplete: No geometry for place:", place);
                // User entered the name of a Place that was not suggested OR pressed the Enter key without selecting.
                // Trigger manual search for the input value.
                if (addressInput.value.trim()) {
                    performGeocodingSearch(addressInput.value.trim());
                }
                return;
            }

            googleMap.setCenter(place.geometry.location);
            googleMap.setZoom(17); // Zoom in more for specific address
            createOrUpdateGoogleMarker(place.geometry.location);
            updateGoogleStateWithLocation(place.geometry.location, place.formatted_address || place.name);
        });


        if (!mapControlsInitialized) {
            const currentLocationControlDiv = document.createElement('div');
            const currentLocationButton = document.createElement('button');
            currentLocationButton.innerHTML = '📍';
            currentLocationButton.title = 'Use My Current Location';
            currentLocationButton.classList.add('custom-map-control-button');
            currentLocationControlDiv.appendChild(currentLocationButton);
            
             if (!googleMap.controls[google.maps.ControlPosition.TOP_RIGHT]) {
                googleMap.controls[google.maps.ControlPosition.TOP_RIGHT] = [];
            }
            googleMap.controls[google.maps.ControlPosition.TOP_RIGHT].push(currentLocationControlDiv);
            mapControlsInitialized = true;

            currentLocationButton.addEventListener('click', () => {
                if (navigator.geolocation) {
                    navigator.geolocation.getCurrentPosition(
                        (position) => {
                            const pos = new google.maps.LatLng(position.coords.latitude, position.coords.longitude);
                            googleMap.setCenter(pos);
                            googleMap.setZoom(15);
                            createOrUpdateGoogleMarker(pos);
                            reverseGeocodeAndUpdateStateGoogle(pos); // Will use Google Geocoder
                        },
                        () => {
                            alert("Error: The Geolocation service failed or was denied.");
                            if (googleMarker && googleMarker.position) {
                               // Try to geocode current marker position if geolocation fails
                               const markerPos = (typeof googleMarker.position.lat === 'function') ? googleMarker.position : new google.maps.LatLng(googleMarker.position.lat, googleMarker.position.lng);
                               reverseGeocodeAndUpdateStateGoogle(markerPos);
                            }
                        },
                        { enableHighAccuracy: true, timeout: 10000, maximumAge: 0 }
                    );
                } else {
                    alert("Error: Your browser doesn't support geolocation.");
                }
            });
        }
        
        // Initial geocoding if no address is set yet from previous state
        if (!(scState.locationAddress && scState.locationCoords && scState.locationCoords.lat)) {
            if (navigator.geolocation) {
                 navigator.geolocation.getCurrentPosition(
                    (position) => {
                        const pos = new google.maps.LatLng(position.coords.latitude, position.coords.longitude);
                        googleMap.setCenter(pos);
                        googleMap.setZoom(15);
                        createOrUpdateGoogleMarker(pos);
                        reverseGeocodeAndUpdateStateGoogle(pos);
                    },
                    () => { reverseGeocodeAndUpdateStateGoogle(initialPosition); } , 
                    { enableHighAccuracy: true, timeout: 5000, maximumAge: 60000 }
                );
            } else {
                reverseGeocodeAndUpdateStateGoogle(initialPosition);
            }
        }

        searchAddressBtn.addEventListener('click', () => {
            performGeocodingSearch(addressInput.value.trim());
        });

        addressInput.addEventListener('keypress', function (e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                // If Autocomplete has suggestions, Enter usually selects one.
                // If not, or if the user just types and hits Enter, we want to search.
                // The Autocomplete 'place_changed' listener handles selection.
                // This will handle cases where Enter is pressed without an Autocomplete selection.
                performGeocodingSearch(addressInput.value.trim());
            }
        });

        confirmLocationBtn.addEventListener('click', () => {
            if (scState.locationAddress && scState.locationMapLink && scState.locationCoords) { // Ensure coords are available
                // --- BEGIN NEW CODE for Static Map URL ---
                const apiKey = "AIzaSyC9c-7XPRyh9Ln6vadgIOFiK4lkLyBMQ5Y"; // Your Google Maps API Key
                const lat = scState.locationCoords.lat;
                const lng = scState.locationCoords.lng;
                const zoom = 18; // Desired zoom level for the static image (e.g., 17-19 is good)
                const width = 600; // Width of the static image
                const height = 400; // Height of the static image
                const mapType = 'hybrid'; // Matches your interactive map
        
                scState.mapScreenshotUrl = `https://maps.googleapis.com/maps/api/staticmap?center=${lat},${lng}&zoom=${zoom}&size=${width}x${height}&maptype=${mapType}&markers=color:red%7Clabel:L%7C${lat},${lng}&key=${apiKey}`;
                // You can log this to test: console.log("Static Map URL:", scState.mapScreenshotUrl);
                // --- END NEW CODE for Static Map URL ---
        
                scState.currentStep = 2;
                scUpdateStep(scState.currentStep);
            } else {
                alert("Please select a location first or wait for it to load.");
            }
        });
    }

    // Helper function for manual geocoding search
    function performGeocodingSearch(addressQuery) {
        if (!addressQuery) { alert("Please enter an address."); return; }
        if (!geocoder) {
            console.error("Geocoder not initialized for search.");
            alert("Map services are not ready. Please try again shortly.");
            return;
        }

        geocoder.geocode({ 'address': addressQuery }, (results, status) => {
            if (status === google.maps.GeocoderStatus.OK) {
                if (results[0]) {
                    const newPos = results[0].geometry.location;
                    googleMap.setCenter(newPos);
                    googleMap.setZoom(17); // Zoom in more for specific address
                    createOrUpdateGoogleMarker(newPos);
                    updateGoogleStateWithLocation(newPos, results[0].formatted_address);
                } else {
                    alert("No results found for the address: " + addressQuery);
                }
            } else {
                alert("Geocode was not successful for '" + addressQuery + "'. Reason: " + status);
                console.error("Geocode error for query '" + addressQuery + "':", status, results);
            }
        });
    }


    async function createOrUpdateGoogleMarker(position) {
        if (!googleMap || !position) return;
        
        if (!google.maps.marker || !google.maps.marker.AdvancedMarkerElement) {
            console.warn("Advanced Marker library not ready, marker creation deferred or failed.");
            return; 
        }

        if (googleMarker) {
            googleMarker.position = position; 
        } else {
            googleMarker = new google.maps.marker.AdvancedMarkerElement({
                map: googleMap,
                position: position,
                gmpDraggable: true,
                title: "Selected Location"
            });

            googleMarker.addListener('dragend', (event) => { 
                const newPosition = event.latLng; 
                googleMap.panTo(newPosition);
                reverseGeocodeAndUpdateStateGoogle(newPosition); // Will use Google Geocoder
            });
        }
        googleMap.setCenter(position);
    }

    // Updated to use Google Geocoding API
    async function reverseGeocodeAndUpdateStateGoogle(latLng) {
        if (!latLng) return;
        if (!geocoder) {
            console.warn("Geocoder not initialized for reverse geocoding. Falling back to lat/lng.");
            updateGoogleStateWithLocation(latLng, `Lat: ${latLng.lat().toFixed(5)}, Lng: ${latLng.lng().toFixed(5)} (Geocoder not ready)`);
            return;
        }

        geocoder.geocode({ 'location': latLng }, (results, status) => {
            if (status === google.maps.GeocoderStatus.OK) {
                if (results[0]) {
                    updateGoogleStateWithLocation(latLng, results[0].formatted_address);
                } else {
                    // No address found for this location
                    updateGoogleStateWithLocation(latLng, `Lat: ${latLng.lat().toFixed(5)}, Lng: ${latLng.lng().toFixed(5)} (No address found)`);
                }
            } else {
                console.error("Reverse geocode error:", status, results);
                updateGoogleStateWithLocation(latLng, `Lat: ${latLng.lat().toFixed(5)}, Lng: ${latLng.lng().toFixed(5)} (Reverse geocoding failed: ${status})`);
            }
        });
    }

    function updateGoogleStateWithLocation(latLng, address) {
        if (scState && latLng) {
            scState.locationCoords = { lat: latLng.lat(), lng: latLng.lng() };
            scState.locationAddress = address;
            scState.locationMapLink = `https://www.google.com/maps/search/?api=1&query=${latLng.lat()},${latLng.lng()}`;

            const addressInputField = document.getElementById('address-input');
            const currentStepEl = document.querySelector('.solar-calculator-step.active');
            if (addressInputField && currentStepEl && currentStepEl.dataset.step === "1") {
                // Only update if not focused to prevent interfering with Autocomplete typing
                if (document.activeElement !== addressInputField) {
                    addressInputField.value = address;
                }
            }
        }
    }
    // --- END GOOGLE MAPS LOGIC ---


    let scState;
    let scUpdateStep;

    // --- CUSTOMIZE YOUR WEEKLY SLOTS HERE ---
    const WEEKLY_AVAILABLE_SLOTS = { // Times in HH:MM (24-hour format for internal use)
        "Monday":    ["09:00", "09:30", "10:00", "10:30", "11:00", "11:30", "13:00", "13:30", "14:00", "14:30", "15:00", "15:30", "16:00"],
        "Tuesday":   ["09:00", "09:30", "10:00", "10:30", "11:00", "11:30", "13:00", "13:30", "14:00", "14:30", "15:00", "15:30", "16:00"],
        "Wednesday": ["09:00", "09:30", "10:00", "10:30", "11:00", "11:30", "13:00", "13:30", "14:00", "14:30", "15:00", "15:30", "16:00"],
        "Thursday":  ["09:00", "09:30", "10:00", "10:30", "11:00", "11:30", "13:00", "13:30", "14:00", "14:30", "15:00", "15:30", "16:00"],
        "Friday":    ["09:00", "09:30", "10:00", "10:30", "11:00", "11:30", "13:00", "13:30", "14:00"],
        "Saturday":  [], 
        "Sunday":    []  
    };
    const DAY_NAMES = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];
    const MONTH_NAMES = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];


    (function() { // Main IIFE for calculator logic
        scState = { 
            currentStep: 1,
            locationAddress: null, locationCoords: null, locationMapLink: null,
            owner: null, installation: null, heating: null, household: null,
            
            selectedDate: null,       
            selectedDateTime: null,   
            calendarCurrentMonth: new Date().getMonth(), 
            calendarCurrentYear: new Date().getFullYear(), 
            isCalendarOpen: false,
            mapScreenshotUrl: null // <-- ADD THIS LINE
        };
    
        const component = document.querySelector('.solar-calculator');
        if (!component) return; 

        const steps = component.querySelectorAll('.solar-calculator-step');
        const progressSteps = component.querySelectorAll('.solar-calculator-progress-step');
        const backButton = component.querySelector('.solar-calculator-back');
        const options = component.querySelectorAll('.solar-calculator-option');
        const noSection = component.querySelector('.solar-calculator-no');
        const balconyLink = component.querySelector('.solar-calculator-balcony-link');
        const container = component.querySelector('.solar-calculator-container');
        const outsideConsultationButton = document.getElementById("outside-consultation-button");
        const contactForm = document.getElementById("uk-contact-form"); 

        let selectedDateDisplayEl, calendarPopupEl, calendarMonthYearEl, calendarPrevMonthBtn, 
            calendarNextMonthBtn, calendarDaysGridEl, timeSlotsContainer, timeSlotsPlaceholder,
            fullNameField, emailField, phoneField, postalCodeField, 
            selectedDateTimeInput, schedulerSendBtn,
            dynamicSchedulerPartsEl; 

        let schedulerFullyInitialized = false;
        let globalClickListenerAttached = false; // For calendar click-outside

        let isTimeSlotDragging = false;
        let timeSlotDragStartX; 
        let timeSlotDragScrollLeftStart;

        function makeTimeSlotsDraggable() {
            if (!timeSlotsContainer) return;
            if (timeSlotsContainer.dataset.draggableInitialized === 'true') return;

            const startDragging = (e) => {
                if (e.button !== 0) return; 
                isTimeSlotDragging = true; timeSlotsContainer.classList.add('dragging');
                timeSlotDragStartX = e.pageX; timeSlotDragScrollLeftStart = timeSlotsContainer.scrollLeft;
            };
            const stopDragging = () => {
                if (!isTimeSlotDragging) return;
                isTimeSlotDragging = false; timeSlotsContainer.classList.remove('dragging');
            };
            const drag = (e) => {
                if (!isTimeSlotDragging) return; e.preventDefault(); 
                const currentX = e.pageX; const walk = currentX - timeSlotDragStartX;
                timeSlotsContainer.scrollLeft = timeSlotDragScrollLeftStart - walk; 
            };
            timeSlotsContainer.addEventListener('mousedown', startDragging);
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', stopDragging);
            timeSlotsContainer.dataset.draggableInitialized = 'true';
        }
        
        function cacheSchedulerDOMElements() { 
            selectedDateDisplayEl = document.getElementById('selected-date-display');
            calendarPopupEl = document.getElementById('calendar-popup');
            calendarMonthYearEl = document.getElementById('calendar-month-year');
            calendarPrevMonthBtn = document.getElementById('calendar-prev-month');
            calendarNextMonthBtn = document.getElementById('calendar-next-month');
            calendarDaysGridEl = document.getElementById('calendar-days');

            timeSlotsContainer = document.getElementById('time-slots-container');
            timeSlotsPlaceholder = document.getElementById('time-slots-placeholder');
            
            fullNameField = document.getElementById('fullName'); 
            emailField = document.getElementById('email');       
            phoneField = document.getElementById('phone');       
            postalCodeField = document.getElementById('postalCode'); 
            
            selectedDateTimeInput = document.getElementById('selected-datetime-input');
            schedulerSendBtn = document.getElementById('scheduler-send-btn');
            dynamicSchedulerPartsEl = document.getElementById('dynamic-scheduler-parts');
        }
        
        scUpdateStep = function(step) { 
            steps.forEach((s) => s.classList.remove('active'));
            noSection.classList.remove('active');
            progressSteps.forEach((s) => {
                s.classList.remove('active', 'completed');
                const stepNum = parseInt(s.dataset.step);
                if (stepNum < step) s.classList.add('completed');
                else if (stepNum === step) s.classList.add('active');
            });

            if (step === 'if-no') {
                noSection.classList.add('active');
                container.classList.add('no-active');
                progressSteps.forEach((s) => { if (parseInt(s.dataset.step) <= 2) s.classList.add('completed'); });
            } else {
                const currentStepElement = component.querySelector(`.solar-calculator-step[data-step="${step}"]`);
                if (currentStepElement) {
                    currentStepElement.classList.add('active');
                    container.classList.remove('no-active'); 
                    
                    if (step === 1) {
                        if (googleMapsApiReady && !googleMap) { 
                            initGoogleMap();
                        } else if (googleMapsApiReady && googleMap) { 
                            setTimeout(() => { 
                                google.maps.event.trigger(googleMap, 'resize');
                                if (scState.locationCoords && scState.locationCoords.lat) {
                                    googleMap.setCenter(new google.maps.LatLng(scState.locationCoords.lat, scState.locationCoords.lng));
                                } else if (googleMarker && googleMarker.position) {
                                    let markerPos = googleMarker.position;
                                    if(markerPos.lat && markerPos.lng && typeof markerPos.lat === 'function'){
                                        googleMap.setCenter(markerPos);
                                    } else if(markerPos.lat && markerPos.lng) { 
                                        googleMap.setCenter(new google.maps.LatLng(markerPos.lat, markerPos.lng));
                                    }
                                }
                            }, 50); 
                        }
                    }


                    if (step === 6) {
                        cacheSchedulerDOMElements(); 
                        makeTimeSlotsDraggable(); 
                        if (dynamicSchedulerPartsEl) dynamicSchedulerPartsEl.style.display = 'block'; 
                        
                        calculateResults(); 

                        if (!schedulerFullyInitialized) { 
                            initNewScheduler(); 
                            schedulerFullyInitialized = true; 
                        } else {
                            if (scState.selectedDate) {
                                updateSelectedDateDisplay();
                                renderCalendar(); 
                                renderTimeSlotsForDate(scState.selectedDate); 
                            } else {
                                const localToday = new Date();
                                scState.selectedDate = localToday.getFullYear() + '-' +
                                                       (localToday.getMonth() + 1).toString().padStart(2, '0') + '-' +
                                                       localToday.getDate().toString().padStart(2, '0');
                                scState.calendarCurrentMonth = localToday.getMonth();
                                scState.calendarCurrentYear = localToday.getFullYear();
                                updateSelectedDateDisplay();
                                renderCalendar();
                                renderTimeSlotsForDate(scState.selectedDate);
                            }
                        }
                    }
                }
            }
            backButton.style.display = (step === 1 || step === 'if-no') ? 'none' : 'flex';
            balconyLink.style.display = (step === 3) ? 'block' : 'none';
            if (outsideConsultationButton) { outsideConsultationButton.classList.add('hidden'); }
            
            if (step !== 6) { 
                resetSegmentAnimations();
                const resultsSectionEl = component.querySelector('.solar-calculator-step[data-step="6"] .solar-calculator-results');
                if (resultsSectionEl) {
                    resultsSectionEl.classList.remove('active');
                }
            } else {
                scState.selectedDateTime = null; 
                if (selectedDateTimeInput) selectedDateTimeInput.value = "";
                if (timeSlotsContainer) {
                    timeSlotsContainer.querySelectorAll('.time-slot-button.selected').forEach(btn => btn.classList.remove('selected'));
                }
                if (schedulerSendBtn) schedulerSendBtn.disabled = true;
                document.querySelectorAll("#uk-contact-form .error-message").forEach(el => el.remove());
                if(contactForm) contactForm.reset();
            }
        }
            
        function resetSegmentAnimations() { 
            const s1=component.querySelector('.solar-calculator-segment-1'), s2=component.querySelector('.solar-calculator-segment-2'), s3=component.querySelector('.solar-calculator-segment-3');
            if(!s1||!s2||!s3)return; s1.style.width='0%';s2.style.width='0%';s3.style.width='0%';s2.style.left='0%';s3.style.left='0%';
         }
        function calculateResults() { 
            if (!scState.owner || !scState.installation || !scState.heating || !scState.household) { return; }
            const resultsSection = component.querySelector('.solar-calculator-step[data-step="6"] .solar-calculator-results');
            if (!resultsSection) return;
            const savingsAmount = resultsSection.querySelector('.solar-calculator-savings-amount');
            const segment1 = resultsSection.querySelector('.solar-calculator-segment-1');
            const segment2 = resultsSection.querySelector('.solar-calculator-segment-2');
            const segment3 = resultsSection.querySelector('.solar-calculator-segment-3');
            const breakdownLabels = resultsSection.querySelector('.solar-calculator-breakdown-labels');
            const features = resultsSection.querySelectorAll('.solar-calculator-feature-text'); 
            const summaryItems = resultsSection.querySelectorAll('.solar-calculator-summary-item'); 
            const disclaimer = resultsSection.querySelector('.solar-calculator-disclaimer');
            if (!savingsAmount || !segment1 || !segment2 || !segment3 || !breakdownLabels || features.length < 2 || summaryItems.length < 4 || !disclaimer) { return; }
            const ELECTRICITY_RATE = 0.245, EXPORT_RATE = 0.05, SOLAR_GENERATION_PER_KW = 850, CO2_PER_KWH = 0.233, YEARS_PROJECTION = 20, DEGRADATION_RATE = 0.005, AUTARKY_RATE = 0.8, PRICE_SIGNAL_CAPABILITY = 4000;
            const INSTALLATION_COSTS = { gable: 6000, flat: 7000, pult: 6500, other: 7500 };
            const HOUSEHOLD_CONSUMPTION = { '1-2': 1800, '3': 2900, '4': 3800, '5-plus': 4300 };
            const annualConsumption = HOUSEHOLD_CONSUMPTION[scState.household];
            const systemSizeKW = Math.max(1, Math.ceil(annualConsumption / SOLAR_GENERATION_PER_KW)); 
            const totalGeneration = Array.from({ length: YEARS_PROJECTION }, (_, year) => systemSizeKW * SOLAR_GENERATION_PER_KW * Math.pow(1 - DEGRADATION_RATE, year)).reduce((a, b) => a + b, 0);
            const selfConsumedEnergy = totalGeneration * AUTARKY_RATE;
            const exportedEnergy = totalGeneration * (1 - AUTARKY_RATE);
            const totalSavingsFromElectricity = selfConsumedEnergy * ELECTRICITY_RATE;
            const totalExportEarnings = exportedEnergy * EXPORT_RATE;
            const totalFinancialBenefits = totalSavingsFromElectricity + totalExportEarnings + PRICE_SIGNAL_CAPABILITY;
            const installationCost = INSTALLATION_COSTS[scState.installation] || 7000;
            const netSavings = totalFinancialBenefits - installationCost;
            const priceSignalPercent = (PRICE_SIGNAL_CAPABILITY / totalFinancialBenefits) * 100;
            const feedInTariffPercent = (totalExportEarnings / totalFinancialBenefits) * 100;
            const electricitySavingsPercent = (totalSavingsFromElectricity / totalFinancialBenefits) * 100;
            const annualCO2Savings = systemSizeKW * SOLAR_GENERATION_PER_KW * CO2_PER_KWH;
            const totalCO2Savings = annualCO2Savings * YEARS_PROJECTION;
            const annualNetBenefit = (totalSavingsFromElectricity + totalExportEarnings) / YEARS_PROJECTION;
            const paybackPeriod = annualNetBenefit > 0 ? (installationCost / annualNetBenefit).toFixed(1) : "N/A";
            savingsAmount.textContent = `£${Math.round(netSavings).toLocaleString()}`;
            resetSegmentAnimations();
            const baseDelay = 500;
            setTimeout(() => { segment1.style.width = `${priceSignalPercent}%`; }, baseDelay);
            setTimeout(() => { segment2.style.left = `${priceSignalPercent}%`; segment2.style.width = `${feedInTariffPercent}%`; }, baseDelay * 2);
            setTimeout(() => { segment3.style.left = `${priceSignalPercent + feedInTariffPercent}%`; segment3.style.width = `${electricitySavingsPercent}%`; }, baseDelay * 3);
            breakdownLabels.innerHTML = `<div>£${PRICE_SIGNAL_CAPABILITY.toLocaleString()}<br>Price signal capability</div><div>+ £${Math.round(totalExportEarnings).toLocaleString()}<br>Feed-in tariff</div><div>+ £${Math.round(totalSavingsFromElectricity).toLocaleString()}<br>Electricity cost savings</div>`;
            features[0].innerHTML = `for up to<br><span class="solar-calculator-feature-value">${Math.round(AUTARKY_RATE * 100)}% Autarky</span>`;
            features[1].innerHTML = `Total CO₂ savings<br><span class="solar-calculator-feature-value">${Math.round(totalCO2Savings).toLocaleString()} kg</span>`;
            summaryItems[0].querySelector('span').textContent = scState.owner === 'yes' ? 'You are the owner' : 'You are not the owner';
            summaryItems[1].querySelector('span').textContent = `Your house is heated with ${scState.heating ? capitalizeFirstLetter(scState.heating) : 'N/A'}`;
            summaryItems[2].querySelector('span').textContent = `Mounting on ${scState.installation ? capitalizeFirstLetter(scState.installation) : 'N/A'} roof`;
            summaryItems[3].querySelector('span').textContent = `${scState.household || 'N/A'} people live in the household`;
            disclaimer.textContent = `Estimated payback period: ${paybackPeriod} years. All calculations are approximate and may vary based on actual conditions.`;
            resultsSection.classList.add('active');
        }

        function initNewScheduler() {
            if (!selectedDateDisplayEl || !supabase) return; 
            if (schedulerSendBtn) schedulerSendBtn.disabled = true;
            
            if (timeSlotsPlaceholder) { 
                timeSlotsPlaceholder.textContent = "Please select a date to see available times."; 
                timeSlotsPlaceholder.style.display = 'block'; 
                if(timeSlotsContainer && !timeSlotsContainer.contains(timeSlotsPlaceholder)) {
                    timeSlotsContainer.innerHTML = ''; 
                    timeSlotsContainer.appendChild(timeSlotsPlaceholder);
                } else if (timeSlotsContainer) {
                     timeSlotsContainer.innerHTML = ''; 
                     timeSlotsContainer.appendChild(timeSlotsPlaceholder);
                }
            } else if(timeSlotsContainer) {
                 timeSlotsContainer.innerHTML = '<p id="time-slots-placeholder" style="color: #666; width:100%; text-align:center; padding:0.5rem 0;">Please select a date to see available times.</p>';
                 timeSlotsPlaceholder = document.getElementById('time-slots-placeholder');
            }


            const localToday = new Date();
            scState.selectedDate = localToday.getFullYear() + '-' +
                                   (localToday.getMonth() + 1).toString().padStart(2, '0') + '-' +
                                   localToday.getDate().toString().padStart(2, '0');
            scState.calendarCurrentMonth = localToday.getMonth();
            scState.calendarCurrentYear = localToday.getFullYear();
            
            updateSelectedDateDisplay();
            renderCalendar(); 
            renderTimeSlotsForDate(scState.selectedDate); 

            if (selectedDateDisplayEl && !selectedDateDisplayEl.dataset.listenerAttached) {
                selectedDateDisplayEl.addEventListener('click', () => toggleCalendar());
                selectedDateDisplayEl.dataset.listenerAttached = 'true';
            }
            if (calendarPrevMonthBtn && !calendarPrevMonthBtn.dataset.listenerAttached) {
                calendarPrevMonthBtn.addEventListener('click', () => changeCalendarMonth(-1));
                calendarPrevMonthBtn.dataset.listenerAttached = 'true';
            }
            if (calendarNextMonthBtn && !calendarNextMonthBtn.dataset.listenerAttached) {
                calendarNextMonthBtn.addEventListener('click', () => changeCalendarMonth(1));
                calendarNextMonthBtn.dataset.listenerAttached = 'true';
            }
            
            if (!globalClickListenerAttached) { 
                document.addEventListener('click', (event) => {
                    if (scState.isCalendarOpen && calendarPopupEl && selectedDateDisplayEl &&
                        !calendarPopupEl.contains(event.target) && 
                        event.target !== selectedDateDisplayEl &&
                        !selectedDateDisplayEl.contains(event.target) ) {
                        toggleCalendar(false); 
                    }
                });
                globalClickListenerAttached = true; 
            }
        }

        function toggleCalendar(forceState) {
            scState.isCalendarOpen = typeof forceState === 'boolean' ? forceState : !scState.isCalendarOpen;
            if (calendarPopupEl) {
                calendarPopupEl.classList.toggle('hidden', !scState.isCalendarOpen);
                if (scState.isCalendarOpen) renderCalendar();
            }
        }
        
        function changeCalendarMonth(direction) {
            scState.calendarCurrentMonth += direction;
            if (scState.calendarCurrentMonth < 0) {
                scState.calendarCurrentMonth = 11;
                scState.calendarCurrentYear--;
            } else if (scState.calendarCurrentMonth > 11) {
                scState.calendarCurrentMonth = 0;
                scState.calendarCurrentYear++;
            }
            renderCalendar();
        }

        function renderCalendar() {
            if (!calendarDaysGridEl || !calendarMonthYearEl) return;
            calendarDaysGridEl.innerHTML = ''; 
            calendarMonthYearEl.textContent = `${MONTH_NAMES[scState.calendarCurrentMonth]} ${scState.calendarCurrentYear}`;

            const localToday = new Date();
            const todayDateString = localToday.getFullYear() + '-' +
                                   (localToday.getMonth() + 1).toString().padStart(2, '0') + '-' +
                                   localToday.getDate().toString().padStart(2, '0');

            const firstDayOfMonthInLocal = new Date(scState.calendarCurrentYear, scState.calendarCurrentMonth, 1).getDay();
            const daysInMonthLocal = new Date(scState.calendarCurrentYear, scState.calendarCurrentMonth + 1, 0).getDate();

            DAY_NAMES.forEach(day => {
                const dayNameEl = document.createElement('div');
                dayNameEl.classList.add('day-name');
                dayNameEl.textContent = day.substring(0, 3);
                calendarDaysGridEl.appendChild(dayNameEl);
            });

            for (let i = 0; i < firstDayOfMonthInLocal; i++) {
                calendarDaysGridEl.appendChild(document.createElement('div'));
            }

            for (let day = 1; day <= daysInMonthLocal; day++) {
                const dayEl = document.createElement('div');
                const cellDateString = scState.calendarCurrentYear + '-' +
                                       (scState.calendarCurrentMonth + 1).toString().padStart(2, '0') + '-' +
                                       day.toString().padStart(2, '0');
                dayEl.textContent = day;
                dayEl.dataset.date = cellDateString;

                const cellDateForCompare = new Date(scState.calendarCurrentYear, scState.calendarCurrentMonth, day, 0, 0, 0, 0);
                const todayForCompare = new Date(localToday.getFullYear(), localToday.getMonth(), localToday.getDate(), 0, 0, 0, 0);

                if (cellDateForCompare < todayForCompare) {
                    dayEl.classList.add('disabled'); 
                } else {
                    if (!dayEl.dataset.listenerAttachedToDay) {
                        dayEl.addEventListener('click', handleCalendarDateSelect);
                        dayEl.dataset.listenerAttachedToDay = 'true';
                    }
                }

                if (scState.selectedDate === cellDateString) {
                    dayEl.classList.add('selected-cal-date');
                }
                if (cellDateString === todayDateString) {
                     dayEl.classList.add('today-cal-date');
                }
                calendarDaysGridEl.appendChild(dayEl);
            }
        }

        function handleCalendarDateSelect(event) {
            const newSelectedDateString = event.target.dataset.date;
            if (!newSelectedDateString || event.target.classList.contains('disabled')) return;

            scState.selectedDate = newSelectedDateString;
            scState.selectedDateTime = null; 
            if (selectedDateTimeInput) selectedDateTimeInput.value = "";
            if (schedulerSendBtn) schedulerSendBtn.disabled = true;

            updateSelectedDateDisplay();
            toggleCalendar(false); 
            renderTimeSlotsForDate(scState.selectedDate);
            renderCalendar(); 
        }
        
        function updateSelectedDateDisplay() { 
            if (!selectedDateDisplayEl || !scState.selectedDate) {
                if(selectedDateDisplayEl) selectedDateDisplayEl.textContent = "Select a Date";
                return;
            }
            const parts = scState.selectedDate.split('-');
            const year = parseInt(parts[0], 10);
            const month = parseInt(parts[1], 10) - 1; 
            const day = parseInt(parts[2], 10);
            
            const dateObjForDisplay = new Date(year, month, day); 
            
            selectedDateDisplayEl.textContent = dateObjForDisplay.toLocaleDateString('en-GB', { 
                day: 'numeric', 
                month: 'long', 
                year: 'numeric'
            });
        }

        async function renderTimeSlotsForDate(dateStringYYYYMMDD) { 
            if (!timeSlotsContainer || !timeSlotsPlaceholder || !supabase) return;
            
            timeSlotsContainer.innerHTML = ''; 
            timeSlotsPlaceholder.textContent = "Loading times...";
            timeSlotsPlaceholder.style.display = 'block';
            if (!timeSlotsContainer.contains(timeSlotsPlaceholder)) {
                 timeSlotsContainer.appendChild(timeSlotsPlaceholder);
            }


            const selectedLocalDate = new Date(dateStringYYYYMMDD + "T00:00:00"); 
            const dayOfWeek = DAY_NAMES[selectedLocalDate.getDay()];
            const baseSlotsForDay = WEEKLY_AVAILABLE_SLOTS[dayOfWeek] || [];

            if (baseSlotsForDay.length === 0) {
                timeSlotsPlaceholder.textContent = "No standard slots available for this day.";
                return;
            }
            
            const year = selectedLocalDate.getFullYear();
            const month = selectedLocalDate.getMonth(); 
            const day = selectedLocalDate.getDate();

            const startOfDayUTC = new Date(Date.UTC(year, month, day, 0, 0, 0, 0)).toISOString();
            const endOfDayUTC = new Date(Date.UTC(year, month, day, 23, 59, 59, 999)).toISOString();
            
            let bookedLocalTimes = [];
            try {
                const { data: bookedAppointments, error: slotsError } = await supabase
                    .from('appointments') 
                    .select('appointment_datetime') 
                    .gte('appointment_datetime', startOfDayUTC)
                    .lte('appointment_datetime', endOfDayUTC);

                if (slotsError) {
                    console.error('Error fetching appointments for date:', dateStringYYYYMMDD, slotsError.message);
                    timeSlotsPlaceholder.textContent = "Error loading times. Please try again.";
                    return;
                }
                bookedLocalTimes = bookedAppointments.map(slot => {
                    const localDateTime = new Date(slot.appointment_datetime);
                    const hours = localDateTime.getHours().toString().padStart(2, '0');
                    const minutes = localDateTime.getMinutes().toString().padStart(2, '0');
                    return `${hours}:${minutes}`;
                });
            } catch (err) {
                console.error('Exception fetching appointments:', err);
                timeSlotsPlaceholder.textContent = "Error loading times. Please try again.";
                return;
            }
            
            timeSlotsContainer.innerHTML = ''; 
            let atLeastOneSlotEnabled = false;

            baseSlotsForDay.forEach(timeHHMM => { 
                const [hours, minutes] = timeHHMM.split(':').map(n => parseInt(n,10));
                const slotDateTimeForDisplay = new Date(year, month, day, hours, minutes);

                const displayTime = slotDateTimeForDisplay.toLocaleTimeString('en-GB', { 
                    hour: '2-digit', 
                    minute: '2-digit', 
                    hour12: false 
                });
                
                const button = document.createElement('button');
                button.type = 'button';
                button.classList.add('time-slot-button');
                button.textContent = displayTime; 
                button.dataset.time = timeHHMM; 

                const isBooked = bookedLocalTimes.includes(timeHHMM);

                if (isBooked) {
                    button.classList.add('disabled');
                    button.disabled = true; 
                } else {
                    atLeastOneSlotEnabled = true;
                    if (!button.dataset.listenerAttachedToSlot) { 
                        button.addEventListener('click', function() { handleTimeSlotSelection(this); });
                        button.dataset.listenerAttachedToSlot = 'true';
                    }
                }
                timeSlotsContainer.appendChild(button);
            });

            if (!atLeastOneSlotEnabled && baseSlotsForDay.length > 0) { 
                timeSlotsPlaceholder.textContent = "All time slots for this date are booked.";
                timeSlotsPlaceholder.style.display = 'block';
                if (!timeSlotsContainer.contains(timeSlotsPlaceholder)) {
                     timeSlotsContainer.appendChild(timeSlotsPlaceholder);
                }
            } else if (baseSlotsForDay.length > 0 && atLeastOneSlotEnabled) { 
                 timeSlotsPlaceholder.style.display = 'none';
            }
        }

        function handleTimeSlotSelection(selectedButton) { 
            if (selectedButton.disabled || selectedButton.classList.contains('disabled')) return;

            if (timeSlotsContainer) {
                timeSlotsContainer.querySelectorAll('.time-slot-button.selected').forEach(btn => btn.classList.remove('selected'));
            }
            
            selectedButton.classList.add('selected');
            const selectedTimeHHMM = selectedButton.dataset.time; 

            const parts = scState.selectedDate.split('-');
            const year = parseInt(parts[0], 10);
            const month = parseInt(parts[1], 10) - 1; 
            const day = parseInt(parts[2], 10);
            const [hours, minutes] = selectedTimeHHMM.split(':').map(n => parseInt(n,10));

            const localDateTime = new Date(year, month, day, hours, minutes, 0, 0);
            scState.selectedDateTime = localDateTime.toISOString(); 
            
            if (selectedDateTimeInput) selectedDateTimeInput.value = scState.selectedDateTime;
            if (schedulerSendBtn) schedulerSendBtn.disabled = false;
        }
    
        options.forEach(option => { 
            option.addEventListener('click', () => {
                const stepElement = option.closest('.solar-calculator-step');
                const stepNumber = parseInt(stepElement.dataset.step);
                stepElement.querySelectorAll('.solar-calculator-option').forEach(opt => opt.classList.remove('selected'));
                option.classList.add('selected');
                const value = option.dataset.value;
                switch (stepNumber) {
                    case 2: scState.owner = value; if (value === 'no') { scState.currentStep = 'if-no'; scUpdateStep('if-no'); return; } break;
                    case 3: scState.installation = value; break;
                    case 4: scState.heating = value; break;
                    case 5: scState.household = value; scState.currentStep = 6; scUpdateStep(6); return; 
                }
                if (typeof scState.currentStep === 'number' && scState.currentStep < 5) { scState.currentStep = stepNumber + 1; scUpdateStep(scState.currentStep); }
            });
        });
    
        backButton.addEventListener('click', (e) => { 
            e.preventDefault();
            if (scState.currentStep === 'if-no') { scState.currentStep = 2; scUpdateStep(2); return; }
            if (typeof scState.currentStep === 'number' && scState.currentStep > 1) {
                if (scState.currentStep === 6) {
                    scState.selectedDateTime = null; 
                    if(selectedDateTimeInput) selectedDateTimeInput.value = "";
                    if(contactForm) contactForm.reset(); 
                    document.querySelectorAll("#uk-contact-form .error-message").forEach(el => el.remove());
                    if (timeSlotsContainer) timeSlotsContainer.querySelectorAll('.time-slot-button.selected').forEach(btn => btn.classList.remove('selected'));
                    if (schedulerSendBtn) schedulerSendBtn.disabled = true;
                }
                scState.currentStep--;
                scUpdateStep(scState.currentStep);
            }
        });
        
        if (contactForm) { 
            contactForm.addEventListener("submit", async function (event) {
                event.preventDefault();
                if (schedulerSendBtn) schedulerSendBtn.disabled = true; 
                if (schedulerSendBtn) schedulerSendBtn.textContent = "Processing...";

                document.querySelectorAll("#uk-contact-form .error-message").forEach((el) => el.remove());
                let hasErrors = false;

                const fullNameVal = fullNameField ? fullNameField.value.trim() : "";
                const emailVal = emailField ? emailField.value.trim() : ""; 
                const phoneVal = phoneField ? phoneField.value.trim() : ""; 
                const postalCodeVal = postalCodeField ? postalCodeField.value.trim() : "";
            
                const nameRegex = /^[a-zA-Z\s'-]{2,}$/; 
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/; 
                const phoneRegex = /^(\+44|0)7\d{9}$/;
                const ukPostcodeRegex = /^[A-Z]{1,2}\d[A-Z\d]?\s?\d[A-Z]{2}$/i;

                function showError(elementOrId, message) {
                    const field = typeof elementOrId === 'string' ? document.getElementById(elementOrId) : elementOrId;
                    if (!field) return;
                    const errorElement = document.createElement("div");
                    errorElement.className = "error-message";
                    errorElement.textContent = message;

                    if (field === timeSlotsContainer) {
                        field.parentNode.insertBefore(errorElement, field);
                    } else { 
                        field.insertAdjacentElement("afterend", errorElement); 
                    }
                    hasErrors = true;
                }
            
                if (!scState.selectedDateTime) { showError(timeSlotsContainer, "Please select an appointment date and time."); }
                if (!nameRegex.test(fullNameVal)) showError(fullNameField, "Please enter a valid name (letters and spaces only).");
                if (!emailRegex.test(emailVal)) showError(emailField, "Please enter a valid email address.");
                if (!phoneRegex.test(phoneVal)) showError(phoneField, "Please enter a valid UK phone number (e.g., +447123456789 or 07123456789).");
                if (!ukPostcodeRegex.test(postalCodeVal)) showError(postalCodeField, "Please enter a valid UK postal code (e.g., SW1A 1AA).");
            
                if (hasErrors) {
                    if (schedulerSendBtn) schedulerSendBtn.disabled = false;
                    if (schedulerSendBtn) schedulerSendBtn.textContent = "Send";
                    return;
                }
                
                const appointmentDataToInsert = {
                    appointment_datetime: scState.selectedDateTime,
                    full_name: fullNameVal, email: emailVal, phone: phoneVal, postal_code: postalCodeVal,
                    location_address: scState.locationAddress, location_map_link: scState.locationMapLink,
                    location_coords_lat: scState.locationCoords ? scState.locationCoords.lat : null,
                    location_coords_lng: scState.locationCoords ? scState.locationCoords.lng : null,
                    questionnaire_owner: scState.owner, questionnaire_installation: scState.installation,
                    questionnaire_heating: scState.heating, questionnaire_household: scState.household
                };

                try {
                    const { data: insertData, error: insertError } = await supabase
                        .from('appointments')
                        .insert([appointmentDataToInsert])
                        .select();

                    if (insertError) {
                        console.error('Supabase booking insert error:', insertError);
                        if (insertError.code === '23505') { 
                            alert('Sorry, this time slot was just booked by someone else. The available slots will be refreshed.');
                            if (timeSlotsContainer) {
                                timeSlotsContainer.querySelectorAll('.time-slot-button.selected').forEach(btn => btn.classList.remove('selected'));
                            }
                            scState.selectedDateTime = null;
                            if (selectedDateTimeInput) selectedDateTimeInput.value = "";
                            if (schedulerSendBtn) schedulerSendBtn.disabled = true;
                            
                            if (scState.selectedDate) {
                                await renderTimeSlotsForDate(scState.selectedDate); 
                            }
                        } else {
                            alert('An error occurred trying to book the slot. Please try again. Details: ' + insertError.message);
                        }
                        if (schedulerSendBtn) {
                            schedulerSendBtn.disabled = false; 
                            schedulerSendBtn.textContent = "Send";
                        }
                        return; 
                    }
                } catch (dbError) {
                    console.error('Supabase booking general error:', dbError);
                    alert('A critical error occurred trying to book the slot. Please try again.');
                    if (schedulerSendBtn) {
                        schedulerSendBtn.disabled = false; 
                        schedulerSendBtn.textContent = "Send";
                    }
                    return; 
                }
                
                let formattedAppointmentTime = "Not selected";
                if (scState.selectedDateTime) {
                    const dateForEmail = new Date(scState.selectedDateTime); 
                    formattedAppointmentTime = dateForEmail.toLocaleString('en-GB', { 
                        dateStyle: 'full', timeStyle: 'short', timeZone: 'Europe/London' 
                    }); 
                }

                let mapImageHtml = "";
                if (scState.mapScreenshotUrl) {
                    mapImageHtml = `
                        <hr>
                        <h2 style="font-family: Arial, sans-serif; color: #333333;">Location Screenshot:</h2>
                        <p style="text-align: center;">
                            <img src="${scState.mapScreenshotUrl}" alt="Map of selected location" style="max-width: 100%; width: auto; height: auto; border: 1px solid #dddddd; border-radius: 4px;">
                        </p>
                    `;
                }
            
                const emailPayload = { 
                    sender: { email: "<EMAIL>", name: "One Solar System" },
                    to: [{ email: "<EMAIL>", name: "One Solar System" }], 
                    subject: "New Consultation Appointment Request", 
                    htmlContent: `
                        <h1>New Consultation Appointment Request</h1>
                        <p><strong>Selected Appointment Time (Europe/London):</strong> ${formattedAppointmentTime}</p>
                        <p><strong>UTC Slot Datetime (DB):</strong> ${scState.selectedDateTime}</p>
                        <hr>
                        <h2>Contact Details:</h2>
                        <p><strong>Full Name:</strong> ${fullNameVal}</p>
                        <p><strong>Email:</strong> ${emailVal}</p>
                        <p><strong>Phone:</strong> ${phoneVal}</p>
                        <p><strong>Postal Code:</strong> ${postalCodeVal}</p>
                        <hr>
                        <h2>Questionnaire Answers:</h2>
                        <p><strong>Location Address:</strong> ${scState.locationAddress || 'Not provided'}</p>
                        <p><strong>Map Link:</strong> ${scState.locationMapLink ? `<a href="${scState.locationMapLink}" target="_blank">View Map</a>` : 'Not provided'}</p>
                        <p><strong>Coordinates:</strong> Lat: ${scState.locationCoords ? scState.locationCoords.lat.toFixed(5) : 'N/A'}, Lng: ${scState.locationCoords ? scState.locationCoords.lng.toFixed(5) : 'N/A'}</p>
                        <p><strong>Are you the owner?</strong> ${scState.owner ? capitalizeFirstLetter(scState.owner) : 'Not specified'}</p>
                        <p><strong>Installation Location:</strong> ${scState.installation ? capitalizeFirstLetter(scState.installation) : 'Not specified'} roof</p>
                        <p><strong>Heating Type:</strong> ${scState.heating ? capitalizeFirstLetter(scState.heating) : 'Not specified'}</p>
                        <p><strong>Household Size:</strong> ${scState.household || 'Not specified'} people</p>
                        ${mapImageHtml}
                    `,
                };
            
                try { 
                    console.log("Attempting to send email with data:", JSON.stringify(emailPayload, null, 2));
                    const response = await fetch("https://api.brevo.com/v3/smtp/email", {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json",
                            "api-key": "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", 
                        },
                        body: JSON.stringify(emailPayload),
                    });
            
                    console.log("Brevo API Response Status:", response.status, response.statusText);
                    
                    if (response.ok) {
                        console.log("Brevo API Response OK.");

                        // Create Outlook calendar event using Supabase Edge Function
                        try {
                            console.log("Creating Outlook calendar event...");

                            const calendarEventData = {
                                appointment_datetime: scState.selectedDateTime,
                                full_name: fullNameVal,
                                email: emailVal,
                                phone: phoneVal,
                                postal_code: postalCodeVal,
                                location_address: scState.locationAddress,
                                location_map_link: scState.locationMapLink,
                                questionnaire_owner: scState.owner,
                                questionnaire_installation: scState.installation,
                                questionnaire_heating: scState.heating,
                                questionnaire_household: scState.household
                            };

                            // Call Supabase Edge Function for calendar integration
                            const { data: calendarResult, error: calendarError } = await supabase.functions.invoke('create-outlook-event', {
                                body: calendarEventData
                            });

                            if (calendarError) {
                                console.error("Calendar integration error:", calendarError);
                                // Don't show error to user since the booking is still successful
                            } else {
                                console.log("Calendar event created successfully:", calendarResult);
                            }
                        } catch (calendarError) {
                            console.error("Error creating calendar event:", calendarError);
                            // Don't show error to user since the booking is still successful
                        }

                        showThankYouCard();
                    } else {
                        const errorText = await response.text(); 
                        console.error("Brevo API Raw Error Response:", errorText);
                        try {
                            const errorData = JSON.parse(errorText); 
                            console.error("Brevo API Parsed Error Data:", errorData);
                            alert(`Failed to send request email: ${errorData.message || 'Unknown error from Brevo API'}. Your slot is booked. Please contact us.`);
                        } catch (e) {
                            alert(`Failed to send request email. Brevo API returned non-JSON error (Status: ${response.status}). Your slot is booked. Please contact us.`);
                        }
                    }
                } catch (error) {
                    console.error("Network or other error sending email via Brevo:", error);
                    alert("An error occurred while trying to send your request email. Your slot is booked. Please contact us.");
                } finally {
                    if (schedulerSendBtn) schedulerSendBtn.disabled = false;
                    if (schedulerSendBtn) schedulerSendBtn.textContent = "Send";
                }
            });
        }

        function capitalizeFirstLetter(string) { if (!string) return ''; return string.charAt(0).toUpperCase() + string.slice(1); }
        function showThankYouCard() { 
            const calcContainer = document.querySelector('.solar-calculator-container');
            const nav = document.querySelector('.solar-calculator-navigation');
            const card = document.getElementById('thank-you-card');
            if (calcContainer) calcContainer.style.display = 'none';
            if (nav) nav.style.display = 'none';
            if (card) card.classList.add('active');
        }
        
        document.addEventListener('DOMContentLoaded', () => {
            cacheSchedulerDOMElements(); 
            scUpdateStep(scState.currentStep);
        });

    })(); 
  </script>
</body>
</html>
