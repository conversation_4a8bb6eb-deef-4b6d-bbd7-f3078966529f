# Supabase Edge Function Setup for Outlook Calendar Integration

Since you want everything in one HTML file, I've implemented the Outlook calendar integration using Supabase Edge Functions. Here's how to set it up:

## Step 1: Create the Edge Function

1. Go to your Supabase Dashboard
2. Navigate to "Edge Functions" in the sidebar
3. Click "Create a new function"
4. Name it: `create-outlook-event`
5. Use this code:

```typescript
// @deno-types="https://esm.sh/@supabase/functions-js/dist/module/types.d.ts"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

Deno.serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { 
      appointment_datetime, 
      full_name, 
      email, 
      phone, 
      postal_code, 
      location_address,
      location_map_link,
      questionnaire_owner,
      questionnaire_installation,
      questionnaire_heating,
      questionnaire_household
    } = await req.json()

    // Microsoft Graph API configuration
    const clientId = Deno.env.get('APPLICATION_ID')
    const clientSecret = Deno.env.get('SECRET_KEY')
    const tenantId = Deno.env.get('DIRECTORY_ID')

    if (!clientId || !clientSecret || !tenantId) {
      throw new Error('Missing Microsoft Graph credentials')
    }

    // Get access token
    const tokenResponse = await fetch(`https://login.microsoftonline.com/${tenantId}/oauth2/v2.0/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        client_id: clientId,
        client_secret: clientSecret,
        scope: 'https://graph.microsoft.com/.default',
        grant_type: 'client_credentials',
      }),
    })

    const tokenData = await tokenResponse.json()
    
    if (!tokenResponse.ok) {
      throw new Error(`Token request failed: ${tokenData.error_description}`)
    }

    // Create calendar event
    const startTime = new Date(appointment_datetime)
    const endTime = new Date(startTime.getTime() + (60 * 60 * 1000)) // 1 hour duration

    const event = {
      subject: `Solar Consultation - ${full_name}`,
      body: {
        contentType: 'HTML',
        content: `
          <h3>Solar Consultation Appointment</h3>
          <p><strong>Client:</strong> ${full_name}</p>
          <p><strong>Email:</strong> ${email}</p>
          <p><strong>Phone:</strong> ${phone}</p>
          <p><strong>Postal Code:</strong> ${postal_code}</p>
          <hr>
          <p><strong>Location:</strong> ${location_address || 'Not specified'}</p>
          ${location_map_link ? `<p><strong>Map Link:</strong> <a href="${location_map_link}">View on Map</a></p>` : ''}
          <hr>
          <h4>Questionnaire Responses:</h4>
          <p><strong>Property Owner:</strong> ${questionnaire_owner || 'Not specified'}</p>
          <p><strong>Installation Type:</strong> ${questionnaire_installation || 'Not specified'}</p>
          <p><strong>Heating Type:</strong> ${questionnaire_heating || 'Not specified'}</p>
          <p><strong>Household Size:</strong> ${questionnaire_household || 'Not specified'}</p>
        `
      },
      start: {
        dateTime: startTime.toISOString(),
        timeZone: 'Europe/London'
      },
      end: {
        dateTime: endTime.toISOString(),
        timeZone: 'Europe/London'
      },
      location: {
        displayName: location_address || 'Client Location'
      },
      attendees: [
        {
          emailAddress: {
            address: email,
            name: full_name
          },
          type: 'required'
        }
      ],
      isReminderOn: true,
      reminderMinutesBeforeStart: 30,
      categories: ['Solar Consultation'],
      importance: 'normal'
    }

    // Create the event
    const eventResponse = await fetch('https://graph.microsoft.com/v1.0/me/events', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${tokenData.access_token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(event),
    })

    const eventData = await eventResponse.json()

    if (!eventResponse.ok) {
      throw new Error(`Event creation failed: ${eventData.error?.message || 'Unknown error'}`)
    }

    return new Response(
      JSON.stringify({
        success: true,
        eventId: eventData.id,
        webLink: eventData.webLink
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )
  } catch (error) {
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      },
    )
  }
})
```

## Step 2: Set Environment Variables

In your Supabase project settings, go to "Settings" > "Environment Variables" and add:

```
APPLICATION_ID=your_azure_app_client_id
SECRET_KEY=your_azure_app_client_secret
DIRECTORY_ID=your_azure_tenant_id
```

## Step 3: Deploy the Function

1. Save the function in Supabase
2. Deploy it
3. Make sure it's enabled

## Step 4: Azure App Registration (if not done already)

1. Go to Azure Portal > Azure Active Directory > App registrations
2. Create new registration
3. Set API permissions:
   - Microsoft Graph > Application permissions
   - Calendars.ReadWrite
   - User.Read.All
4. Grant admin consent
5. Create client secret
6. Copy the Application ID, Directory ID, and Secret to Supabase environment variables

## That's it!

Your Widget.html file is now ready and will automatically create Outlook calendar events when bookings are made. The integration happens through the Supabase Edge Function, keeping everything serverless and simple.

## Testing

After setting up the Edge Function, test a booking through your widget. Check:
1. Supabase Edge Function logs
2. Your Outlook calendar for the new event
3. Browser console for any errors

The booking will still work even if the calendar integration fails, ensuring reliability.
